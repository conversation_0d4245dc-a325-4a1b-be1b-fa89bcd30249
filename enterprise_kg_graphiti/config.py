"""
Configuration settings for Enterprise Knowledge Graph with Graphiti
"""

import os
from typing import List, Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv

load_dotenv()


@dataclass
class Neo4jConfig:
    """Neo4j database configuration."""
    uri: str = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    user: str = os.getenv('NEO4J_USER', 'neo4j')
    password: str = os.getenv('NEO4J_PASSWORD', 'password')
    database: str = os.getenv('NEO4J_DATABASE', 'neo4j')


@dataclass
class LLMConfig:
    """LLM configuration for Graphiti."""
    provider: str = os.getenv('LLM_PROVIDER', 'requesty')
    model: str = os.getenv('LLM_MODEL', 'anthropic/claude-3.5-sonnet')
    api_key: str = os.getenv('REQUESTY_API_KEY', '')
    base_url: str = os.getenv('REQUESTY_BASE_URL', 'https://router.requesty.ai/v1')
    temperature: float = float(os.getenv('LLM_TEMPERATURE', '0.1'))
    max_tokens: int = int(os.getenv('LLM_MAX_TOKENS', '4000'))


@dataclass
class EmbedderConfig:
    """Embedder configuration for Graphiti."""
    provider: str = os.getenv('EMBEDDER_PROVIDER', 'simple')
    model: str = os.getenv('EMBEDDER_MODEL', 'simple-embedder')
    embedding_dim: int = int(os.getenv('EMBEDDING_DIM', '384'))


@dataclass
class ProcessingConfig:
    """Document processing configuration."""
    chunk_size: int = int(os.getenv('CHUNK_SIZE', '1000'))
    chunk_overlap: int = int(os.getenv('CHUNK_OVERLAP', '200'))
    min_chunk_size: int = int(os.getenv('MIN_CHUNK_SIZE', '100'))
    supported_extensions: List[str] = None
    group_id: str = os.getenv('GROUP_ID', 'enterprise_kg')
    update_communities: bool = os.getenv('UPDATE_COMMUNITIES', 'true').lower() == 'true'
    store_raw_content: bool = os.getenv('STORE_RAW_CONTENT', 'true').lower() == 'true'

    def __post_init__(self):
        if self.supported_extensions is None:
            self.supported_extensions = ['.txt', '.md', '.json', '.docx', '.pdf']


@dataclass
class SearchConfig:
    """Search configuration."""
    default_limit: int = int(os.getenv('SEARCH_DEFAULT_LIMIT', '10'))
    max_limit: int = int(os.getenv('SEARCH_MAX_LIMIT', '100'))
    default_search_type: str = os.getenv('DEFAULT_SEARCH_TYPE', 'hybrid')


class EnterpriseKGConfig:
    """Main configuration class for Enterprise Knowledge Graph."""

    def __init__(self):
        self.neo4j = Neo4jConfig()
        self.llm = LLMConfig()
        self.embedder = EmbedderConfig()
        self.processing = ProcessingConfig()
        self.search = SearchConfig()

    def validate(self) -> List[str]:
        """
        Validate configuration and return list of errors.

        Returns:
            List of validation error messages
        """
        errors = []

        # Validate Neo4j configuration
        if not self.neo4j.uri:
            errors.append("Neo4j URI is required")
        if not self.neo4j.user:
            errors.append("Neo4j user is required")
        if not self.neo4j.password:
            errors.append("Neo4j password is required")

        # Validate LLM configuration
        if self.llm.provider == 'requesty' and not self.llm.api_key:
            errors.append("Requesty API key is required when using Requesty provider")
        elif self.llm.provider == 'openai' and not self.llm.api_key:
            errors.append("OpenAI API key is required when using OpenAI provider")

        # Validate embedder configuration (simple embedder doesn't need API key)
        if self.embedder.provider == 'openai' and not hasattr(self.embedder, 'api_key'):
            errors.append("OpenAI API key is required when using OpenAI embedder")

        # Validate processing configuration
        if self.processing.chunk_size <= 0:
            errors.append("Chunk size must be positive")
        if self.processing.chunk_overlap < 0:
            errors.append("Chunk overlap cannot be negative")
        if self.processing.chunk_overlap >= self.processing.chunk_size:
            errors.append("Chunk overlap must be less than chunk size")

        return errors

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'neo4j': {
                'uri': self.neo4j.uri,
                'user': self.neo4j.user,
                'password': '***' if self.neo4j.password else '',
                'database': self.neo4j.database
            },
            'llm': {
                'provider': self.llm.provider,
                'model': self.llm.model,
                'api_key': '***' if self.llm.api_key else '',
                'temperature': self.llm.temperature,
                'max_tokens': self.llm.max_tokens
            },
            'embedder': {
                'provider': self.embedder.provider,
                'model': self.embedder.model,
                'api_key': '***' if self.embedder.api_key else ''
            },
            'processing': {
                'chunk_size': self.processing.chunk_size,
                'chunk_overlap': self.processing.chunk_overlap,
                'min_chunk_size': self.processing.min_chunk_size,
                'supported_extensions': self.processing.supported_extensions,
                'group_id': self.processing.group_id,
                'update_communities': self.processing.update_communities,
                'store_raw_content': self.processing.store_raw_content
            },
            'search': {
                'default_limit': self.search.default_limit,
                'max_limit': self.search.max_limit,
                'default_search_type': self.search.default_search_type
            }
        }


# Default configuration instance
default_config = EnterpriseKGConfig()


def get_config() -> EnterpriseKGConfig:
    """Get the default configuration instance."""
    return default_config


def validate_config() -> List[str]:
    """Validate the default configuration."""
    return default_config.validate()


# Environment variable documentation
ENV_VARS_DOCUMENTATION = """
Environment Variables for Enterprise Knowledge Graph with Graphiti:

Neo4j Configuration:
- NEO4J_URI: Neo4j database URI (default: bolt://localhost:7687)
- NEO4J_USER: Neo4j username (default: neo4j)
- NEO4J_PASSWORD: Neo4j password (default: password)
- NEO4J_DATABASE: Neo4j database name (default: neo4j)

LLM Configuration:
- LLM_PROVIDER: LLM provider (default: openai)
- LLM_MODEL: LLM model name (default: gpt-4o)
- OPENAI_API_KEY: OpenAI API key (required for OpenAI provider)
- LLM_TEMPERATURE: LLM temperature (default: 0.1)
- LLM_MAX_TOKENS: Maximum tokens for LLM (default: 4000)

Embedder Configuration:
- EMBEDDER_PROVIDER: Embedder provider (default: openai)
- EMBEDDER_MODEL: Embedder model name (default: text-embedding-3-small)

Processing Configuration:
- CHUNK_SIZE: Document chunk size in characters (default: 1000)
- CHUNK_OVERLAP: Overlap between chunks in characters (default: 200)
- MIN_CHUNK_SIZE: Minimum chunk size in characters (default: 100)
- GROUP_ID: Group ID for episodes (default: enterprise_kg)
- UPDATE_COMMUNITIES: Whether to update communities (default: true)
- STORE_RAW_CONTENT: Whether to store raw episode content (default: true)

Search Configuration:
- SEARCH_DEFAULT_LIMIT: Default search result limit (default: 10)
- SEARCH_MAX_LIMIT: Maximum search result limit (default: 100)
- DEFAULT_SEARCH_TYPE: Default search type (default: hybrid)
"""
