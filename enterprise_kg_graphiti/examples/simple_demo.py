"""
Simple Demo: Enterprise Knowledge Graph with Graphiti

This is a minimal example showing how to use the enterprise knowledge graph
with your existing documents from enterprise_kg_minimal.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.processor import GraphitiEnterpriseProcessor
from utils.helpers import setup_logging


async def simple_demo():
    """Simple demonstration of the enterprise knowledge graph."""
    
    print("🚀 Enterprise Knowledge Graph with Graphiti - Simple Demo")
    print("=" * 60)
    
    # Set up logging
    setup_logging("INFO")
    
    # Configuration (you can also use environment variables)
    NEO4J_URI = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    NEO4J_USER = os.getenv('NEO4J_USER', 'neo4j')
    NEO4J_PASSWORD = os.getenv('NEO4J_PASSWORD', 'password')
    
    if not all([NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD]):
        print("❌ Please set NEO4J_URI, NEO4J_USER, and NEO4J_PASSWORD environment variables")
        print("   Or update the values in this script")
        return
    
    # Check if we have access to the original documents
    original_docs_path = Path(__file__).parent.parent.parent / "enterprise_kg_minimal" / "documents"
    
    if not original_docs_path.exists():
        print(f"⚠️ Original documents not found at {original_docs_path}")
        print("   Creating sample documents instead...")
        
        # Create sample documents in current directory
        from utils.helpers import create_sample_documents
        docs_path = "./demo_documents"
        create_sample_documents(docs_path)
        print(f"✅ Created sample documents in {docs_path}")
    else:
        docs_path = str(original_docs_path)
        print(f"📁 Using original documents from {docs_path}")
    
    # Initialize the processor
    print("\n🔧 Initializing Graphiti Enterprise Processor...")
    
    processor = GraphitiEnterpriseProcessor(
        neo4j_uri=NEO4J_URI,
        neo4j_user=NEO4J_USER,
        neo4j_password=NEO4J_PASSWORD,
        chunk_size=800,  # Smaller chunks for demo
        chunk_overlap=100
    )
    
    try:
        # Initialize the knowledge graph database
        print("   Setting up knowledge graph indices...")
        await processor.initialize()
        print("✅ Processor initialized successfully")
        
        # Show schema information
        schema_summary = await processor.get_schema_summary()
        print(f"\n📋 Enterprise Schema:")
        print(f"   Entity types: {schema_summary['total_entity_types']}")
        print(f"   Relationship types: {schema_summary['total_relationship_types']}")
        print(f"   Examples: {', '.join(schema_summary['entity_types'][:5])}")
        
        # Process documents
        print(f"\n📄 Processing documents from {docs_path}...")
        results = await processor.process_directory(docs_path)
        
        # Show processing results
        successful = sum(1 for r in results if r['success'])
        total_episodes = sum(r.get('episodes_added', 0) for r in results)
        
        print(f"\n📊 Processing Results:")
        print(f"   Files processed: {len(results)}")
        print(f"   Successful: {successful}")
        print(f"   Knowledge graph episodes: {total_episodes}")
        
        if total_episodes > 0:
            print(f"\n🔍 Testing Search Capabilities:")
            
            # Example searches
            search_queries = [
                "Project Atlas",
                "Priya Sharma",
                "team members",
                "budget",
                "AI platform"
            ]
            
            for query in search_queries:
                print(f"\n   Searching: '{query}'")
                results = await processor.search(query, limit=3)
                
                if results:
                    for i, result in enumerate(results, 1):
                        fact = result.get('fact', result.get('name', 'Unknown'))
                        # Truncate long facts for display
                        if len(fact) > 80:
                            fact = fact[:77] + "..."
                        print(f"     {i}. {fact}")
                else:
                    print(f"     No results found")
            
            # Show entity relationships
            print(f"\n🔗 Entity Relationships Example:")
            print(f"   Finding relationships for 'Priya Sharma'...")
            
            relationships = await processor.get_entity_relationships("Priya Sharma")
            if relationships:
                for i, rel in enumerate(relationships[:3], 1):
                    fact = rel.get('fact', 'Unknown relationship')
                    if len(fact) > 80:
                        fact = fact[:77] + "..."
                    print(f"     {i}. {fact}")
            else:
                print(f"     No relationships found")
            
            print(f"\n✨ Knowledge Graph Successfully Created!")
            print(f"   You can now:")
            print(f"   - Query the Neo4j database directly")
            print(f"   - Use the search interface for complex queries")
            print(f"   - Add more documents to expand the knowledge graph")
            print(f"   - Integrate with your existing enterprise systems")
            
        else:
            print("⚠️ No episodes were created. This might indicate:")
            print("   - Document processing issues")
            print("   - Configuration problems")
            print("   - Empty or unsupported document formats")
    
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        await processor.close()
        print(f"\n✅ Demo completed!")


if __name__ == "__main__":
    # Check for required environment variables
    required_env_vars = ['OPENAI_API_KEY']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these variables and try again.")
        print("Example:")
        print("   export OPENAI_API_KEY='your-api-key-here'")
        print("   export NEO4J_PASSWORD='your-neo4j-password'")
        sys.exit(1)
    
    # Run the demo
    asyncio.run(simple_demo())
