"""
Basic Usage Example for Enterprise Knowledge Graph with Graphiti

This example demonstrates how to use the GraphitiEnterpriseProcessor
to process documents and build a knowledge graph.
"""

import asyncio
import logging
from pathlib import Path

from enterprise_kg_graphiti.core.processor import GraphitiEnterpriseProcessor
from enterprise_kg_graphiti.config import EnterpriseKGConfig
from enterprise_kg_graphiti.utils.helpers import create_sample_documents, setup_logging

# Set up logging
setup_logging("INFO")
logger = logging.getLogger(__name__)


async def basic_example():
    """Basic example of processing documents and searching."""
    
    print("🚀 Enterprise Knowledge Graph with Graphiti - Basic Example")
    print("=" * 60)
    
    # Load configuration
    config = EnterpriseKGConfig()
    
    # Validate configuration
    errors = config.validate()
    if errors:
        print("❌ Configuration errors:")
        for error in errors:
            print(f"  - {error}")
        print("\nPlease set up your environment variables (see README.md)")
        return
    
    # Create sample documents
    print("\n📁 Creating sample documents...")
    sample_dir = "./sample_documents"
    try:
        created_files = create_sample_documents(sample_dir)
        print(f"✅ Created {len(created_files)} sample documents in {sample_dir}")
    except Exception as e:
        print(f"❌ Failed to create sample documents: {e}")
        return
    
    # Initialize processor
    print("\n🔧 Initializing processor...")
    processor = GraphitiEnterpriseProcessor(
        neo4j_uri=config.neo4j.uri,
        neo4j_user=config.neo4j.user,
        neo4j_password=config.neo4j.password,
        chunk_size=800,  # Smaller chunks for demo
        chunk_overlap=100
    )
    
    try:
        # Initialize the knowledge graph
        await processor.initialize()
        print("✅ Processor initialized successfully")
        
        # Show schema information
        schema_summary = await processor.get_schema_summary()
        print(f"\n📋 Schema Summary:")
        print(f"   Entity types: {schema_summary['total_entity_types']}")
        print(f"   Relationship types: {schema_summary['total_relationship_types']}")
        
        # Process the sample documents
        print(f"\n📄 Processing documents from {sample_dir}...")
        results = await processor.process_directory(sample_dir)
        
        # Show results
        successful = sum(1 for r in results if r['success'])
        total_episodes = sum(r.get('episodes_added', 0) for r in results)
        
        print(f"\n📊 Processing Results:")
        print(f"   Files processed: {len(results)}")
        print(f"   Successful: {successful}")
        print(f"   Episodes added: {total_episodes}")
        
        if total_episodes > 0:
            # Perform some example searches
            print(f"\n🔍 Example Searches:")
            
            # Search 1: Find information about Project Atlas
            print(f"\n1. Searching for 'Project Atlas'...")
            results1 = await processor.search("Project Atlas", limit=5)
            for i, result in enumerate(results1[:3], 1):
                print(f"   {i}. {result.get('fact', result.get('name', 'Unknown'))}")
            
            # Search 2: Find team members
            print(f"\n2. Searching for 'team members'...")
            results2 = await processor.search("team members", limit=5)
            for i, result in enumerate(results2[:3], 1):
                print(f"   {i}. {result.get('fact', result.get('name', 'Unknown'))}")
            
            # Search 3: Find project budget information
            print(f"\n3. Searching for 'budget'...")
            results3 = await processor.search("budget", limit=5)
            for i, result in enumerate(results3[:3], 1):
                print(f"   {i}. {result.get('fact', result.get('name', 'Unknown'))}")
            
            # Search 4: Find relationships for a specific entity
            print(f"\n4. Finding relationships for 'Priya Sharma'...")
            relationships = await processor.get_entity_relationships("Priya Sharma")
            for i, rel in enumerate(relationships[:3], 1):
                print(f"   {i}. {rel.get('fact', 'Unknown relationship')}")
        
        else:
            print("⚠️ No episodes were added. Check your documents and configuration.")
    
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        logger.exception("Detailed error:")
    
    finally:
        # Clean up
        await processor.close()
        print("\n✅ Example completed!")


async def advanced_search_example():
    """Example of advanced search capabilities."""
    
    print("\n🔍 Advanced Search Example")
    print("-" * 40)
    
    config = EnterpriseKGConfig()
    processor = GraphitiEnterpriseProcessor(
        neo4j_uri=config.neo4j.uri,
        neo4j_user=config.neo4j.user,
        neo4j_password=config.neo4j.password
    )
    
    try:
        await processor.initialize()
        
        # Search by entity type
        print("\n1. Searching for Person entities...")
        person_entities = await processor.search(
            query="people in the project",
            entity_types=["Person", "Employee", "Manager"],
            limit=5
        )
        
        for entity in person_entities[:3]:
            print(f"   - {entity.get('name', entity.get('fact', 'Unknown'))}")
        
        # Search by relationship type
        print("\n2. Searching for 'works_for' relationships...")
        work_relationships = await processor.search(
            query="who works for whom",
            relationship_types=["works_for", "reports_to", "manages"],
            limit=5
        )
        
        for rel in work_relationships[:3]:
            print(f"   - {rel.get('fact', 'Unknown relationship')}")
        
        # Node-specific search
        print("\n3. Node-specific search...")
        node_results = await processor.search(
            query="project management",
            limit=5
        )
        
        for node in node_results[:3]:
            if node.get('type') == 'node':
                print(f"   - Entity: {node.get('name', 'Unknown')}")
            else:
                print(f"   - Fact: {node.get('fact', 'Unknown')}")
    
    except Exception as e:
        print(f"❌ Advanced search error: {e}")
    
    finally:
        await processor.close()


async def schema_exploration_example():
    """Example of exploring the schema and statistics."""
    
    print("\n📊 Schema Exploration Example")
    print("-" * 40)
    
    config = EnterpriseKGConfig()
    processor = GraphitiEnterpriseProcessor(
        neo4j_uri=config.neo4j.uri,
        neo4j_user=config.neo4j.user,
        neo4j_password=config.neo4j.password
    )
    
    try:
        await processor.initialize()
        
        # Get schema summary
        schema_summary = await processor.get_schema_summary()
        
        print("📋 Supported Entity Types:")
        for entity_type in schema_summary['entity_types'][:10]:
            print(f"   - {entity_type}")
        
        print(f"\n🔗 Supported Relationship Types:")
        for rel_type in schema_summary['relationship_types'][:10]:
            print(f"   - {rel_type}")
        
        # Get processing statistics
        stats = processor.get_processing_stats()
        print(f"\n⚙️ Processing Configuration:")
        print(f"   Chunk size: {stats['chunk_size']} characters")
        print(f"   Chunk overlap: {stats['chunk_overlap']} characters")
        print(f"   Group ID: {stats['group_id']}")
        print(f"   Update communities: {stats['update_communities']}")
        
        # Get search interface statistics
        search_stats = await processor.search_interface.get_schema_statistics()
        if search_stats:
            print(f"\n📈 Knowledge Graph Statistics:")
            print(f"   Entity type counts:")
            for entity_type, count in list(search_stats.get('entity_type_counts', {}).items())[:5]:
                print(f"     {entity_type}: {count}")
            
            print(f"   Relationship type counts:")
            for rel_type, count in list(search_stats.get('relationship_type_counts', {}).items())[:5]:
                print(f"     {rel_type}: {count}")
    
    except Exception as e:
        print(f"❌ Schema exploration error: {e}")
    
    finally:
        await processor.close()


if __name__ == "__main__":
    async def run_all_examples():
        """Run all examples."""
        await basic_example()
        await advanced_search_example()
        await schema_exploration_example()
    
    asyncio.run(run_all_examples())
