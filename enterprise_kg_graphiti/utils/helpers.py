"""
Helper utilities for Enterprise Knowledge Graph with Graphiti
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime


def setup_logging(level: str = "INFO", 
                 format_string: Optional[str] = None,
                 log_file: Optional[str] = None) -> logging.Logger:
    """
    Set up logging configuration.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
        format_string: Custom format string for log messages
        log_file: Optional file to write logs to
        
    Returns:
        Configured logger instance
    """
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=format_string,
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    logger = logging.getLogger('enterprise_kg_graphiti')
    
    # Add file handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(format_string))
        logger.addHandler(file_handler)
    
    return logger


def validate_file_path(file_path: str) -> bool:
    """
    Validate that a file path exists and is readable.
    
    Args:
        file_path: Path to the file
        
    Returns:
        True if file is valid and readable
    """
    try:
        path = Path(file_path)
        return path.exists() and path.is_file() and os.access(path, os.R_OK)
    except Exception:
        return False


def validate_directory_path(directory_path: str) -> bool:
    """
    Validate that a directory path exists and is readable.
    
    Args:
        directory_path: Path to the directory
        
    Returns:
        True if directory is valid and readable
    """
    try:
        path = Path(directory_path)
        return path.exists() and path.is_dir() and os.access(path, os.R_OK)
    except Exception:
        return False


def get_file_info(file_path: str) -> Dict[str, Any]:
    """
    Get information about a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Dictionary with file information
    """
    try:
        path = Path(file_path)
        if not path.exists():
            return {'error': 'File not found'}
        
        stat = path.stat()
        return {
            'name': path.name,
            'size': stat.st_size,
            'extension': path.suffix,
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'created': datetime.fromtimestamp(stat.st_ctime),
            'is_readable': os.access(path, os.R_OK)
        }
    except Exception as e:
        return {'error': str(e)}


def format_processing_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Format processing results into a summary.
    
    Args:
        results: List of processing results
        
    Returns:
        Formatted summary dictionary
    """
    if not results:
        return {
            'total_files': 0,
            'successful': 0,
            'failed': 0,
            'total_episodes': 0,
            'success_rate': 0.0,
            'errors': []
        }
    
    total_files = len(results)
    successful = sum(1 for r in results if r.get('success', False))
    failed = total_files - successful
    total_episodes = sum(r.get('episodes_added', 0) for r in results)
    
    errors = []
    for result in results:
        if not result.get('success', False) and 'error' in result:
            errors.append({
                'file': result.get('file_path', 'Unknown'),
                'error': result['error']
            })
    
    return {
        'total_files': total_files,
        'successful': successful,
        'failed': failed,
        'total_episodes': total_episodes,
        'success_rate': (successful / total_files) * 100 if total_files > 0 else 0.0,
        'errors': errors
    }


def create_sample_documents(output_dir: str) -> List[str]:
    """
    Create sample documents for testing.
    
    Args:
        output_dir: Directory to create sample documents in
        
    Returns:
        List of created file paths
    """
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    created_files = []
    
    # Sample text document
    text_content = """
    Project Atlas - AI-driven Customer Insights Platform
    
    Project Overview:
    Project Atlas is an innovative AI-driven customer insights platform designed to revolutionize 
    how businesses understand and engage with their customers. Led by Priya Sharma, the project 
    aims to integrate advanced machine learning algorithms with real-time data processing to 
    provide actionable customer intelligence.
    
    Key Team Members:
    - Priya Sharma: Project Lead and Data Science Manager
    - John Smith: Senior Software Engineer, responsible for platform architecture
    - Sarah Johnson: UX/UI Designer, leading user experience design
    - Mike Chen: DevOps Engineer, managing infrastructure and deployment
    
    Project Components:
    1. Data Ingestion Engine: Collects customer data from multiple sources
    2. AI Analysis Engine: Processes data using machine learning models
    3. Insights Dashboard: Provides real-time customer insights visualization
    4. API Gateway: Enables integration with existing business systems
    
    Timeline:
    - Phase 1: Data architecture and ingestion (Q1 2024)
    - Phase 2: AI model development and training (Q2 2024)
    - Phase 3: Dashboard development and testing (Q3 2024)
    - Phase 4: Production deployment and optimization (Q4 2024)
    
    Budget: $2.5M allocated for the entire project lifecycle
    Expected ROI: 300% within 18 months of deployment
    """
    
    text_file = output_path / "project_atlas_overview.txt"
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write(text_content)
    created_files.append(str(text_file))
    
    # Sample JSON document
    json_content = {
        "project": {
            "id": "ATLAS-2024",
            "name": "Project Atlas",
            "type": "AI Platform Development",
            "status": "In Progress",
            "lead": {
                "name": "Priya Sharma",
                "role": "Project Lead",
                "department": "Data Science",
                "email": "<EMAIL>"
            },
            "team_members": [
                {
                    "name": "John Smith",
                    "role": "Senior Software Engineer",
                    "department": "Engineering",
                    "responsibilities": ["Platform Architecture", "Backend Development"]
                },
                {
                    "name": "Sarah Johnson",
                    "role": "UX/UI Designer",
                    "department": "Design",
                    "responsibilities": ["User Experience", "Interface Design"]
                },
                {
                    "name": "Mike Chen",
                    "role": "DevOps Engineer",
                    "department": "Infrastructure",
                    "responsibilities": ["CI/CD", "Cloud Infrastructure"]
                }
            ],
            "budget": {
                "total": 2500000,
                "currency": "USD",
                "allocated_by_phase": {
                    "phase_1": 500000,
                    "phase_2": 800000,
                    "phase_3": 700000,
                    "phase_4": 500000
                }
            },
            "technologies": [
                "Python",
                "TensorFlow",
                "Apache Kafka",
                "PostgreSQL",
                "React",
                "Docker",
                "Kubernetes"
            ],
            "milestones": [
                {
                    "name": "Data Architecture Complete",
                    "date": "2024-03-31",
                    "status": "Completed"
                },
                {
                    "name": "AI Models Trained",
                    "date": "2024-06-30",
                    "status": "In Progress"
                },
                {
                    "name": "Dashboard Beta Release",
                    "date": "2024-09-30",
                    "status": "Planned"
                }
            ]
        }
    }
    
    json_file = output_path / "project_atlas_data.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_content, f, indent=2, ensure_ascii=False)
    created_files.append(str(json_file))
    
    # Sample meeting notes
    meeting_content = """
    Meeting Notes: Project Atlas Weekly Standup
    Date: March 15, 2024
    Attendees: Priya Sharma, John Smith, Sarah Johnson, Mike Chen
    
    Agenda:
    1. Progress Updates
    2. Blockers and Issues
    3. Next Week Planning
    
    Progress Updates:
    
    Priya Sharma (Project Lead):
    - Completed stakeholder review meetings
    - Finalized requirements for Phase 2
    - Budget approval received for additional ML infrastructure
    
    John Smith (Senior Software Engineer):
    - Data ingestion pipeline is 90% complete
    - API gateway architecture finalized
    - Working on integration with existing CRM systems
    
    Sarah Johnson (UX/UI Designer):
    - User research completed with 50+ participants
    - Wireframes for insights dashboard approved
    - Starting high-fidelity mockups next week
    
    Mike Chen (DevOps Engineer):
    - Kubernetes cluster setup completed
    - CI/CD pipeline configured for automated testing
    - Security audit scheduled for next month
    
    Blockers and Issues:
    - Waiting for legal approval on data privacy compliance
    - Need additional GPU resources for ML model training
    - Integration with legacy systems taking longer than expected
    
    Action Items:
    - Priya: Follow up with legal team on privacy compliance (Due: March 20)
    - John: Complete CRM integration testing (Due: March 22)
    - Sarah: Present dashboard mockups to stakeholders (Due: March 25)
    - Mike: Procure additional GPU instances (Due: March 18)
    
    Next Meeting: March 22, 2024 at 10:00 AM
    """
    
    meeting_file = output_path / "atlas_meeting_notes_2024_03_15.md"
    with open(meeting_file, 'w', encoding='utf-8') as f:
        f.write(meeting_content)
    created_files.append(str(meeting_file))
    
    return created_files


def print_banner(title: str, width: int = 60):
    """Print a formatted banner with title."""
    print("=" * width)
    print(f"{title:^{width}}")
    print("=" * width)


def print_config_summary(config_dict: Dict[str, Any]):
    """Print a formatted configuration summary."""
    print("\nConfiguration Summary:")
    print("-" * 40)
    
    for section, values in config_dict.items():
        print(f"\n{section.upper()}:")
        for key, value in values.items():
            if isinstance(value, list):
                print(f"  {key}: {', '.join(map(str, value))}")
            else:
                print(f"  {key}: {value}")


def estimate_processing_time(file_count: int, avg_file_size: int) -> str:
    """
    Estimate processing time based on file count and size.
    
    Args:
        file_count: Number of files to process
        avg_file_size: Average file size in bytes
        
    Returns:
        Estimated processing time as string
    """
    # Rough estimates based on typical processing speeds
    base_time_per_file = 2  # seconds
    size_factor = avg_file_size / 10000  # additional time based on size
    
    total_seconds = file_count * (base_time_per_file + size_factor)
    
    if total_seconds < 60:
        return f"{total_seconds:.0f} seconds"
    elif total_seconds < 3600:
        return f"{total_seconds/60:.1f} minutes"
    else:
        return f"{total_seconds/3600:.1f} hours"
