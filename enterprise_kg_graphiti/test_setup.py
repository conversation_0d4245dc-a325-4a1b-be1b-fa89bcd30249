"""
Test Setup for Enterprise Knowledge Graph with Graphiti

This script tests the basic setup and configuration.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from config import EnterpriseKGConfig, validate_config
from core.requesty_client import create_requesty_client
from core.simple_embedder import create_simple_embedder


def test_configuration():
    """Test configuration loading and validation."""
    print("🔧 Testing Configuration...")

    config = EnterpriseKGConfig()
    errors = validate_config()

    if errors:
        print("❌ Configuration errors:")
        for error in errors:
            print(f"  - {error}")
        return False
    else:
        print("✅ Configuration is valid")
        return True


async def test_requesty_client():
    """Test Requesty client initialization."""
    print("\n🤖 Testing Requesty Client...")

    try:
        client = create_requesty_client()
        print("✅ Requesty client created successfully")

        # Test a simple generation (if API key is available)
        if client.api_key:
            print("   Testing API connection...")
            try:
                # Import Message class for proper testing
                from graphiti_core.prompts.models import Message

                response = await client._generate_response([
                    Message(role="user", content="Say 'Hello, Enterprise KG!' in exactly those words.")
                ])
                print(f"   API Response: {response}")
                print("✅ Requesty API is working")
            except Exception as e:
                print(f"   ⚠️ API test failed: {e}")
        else:
            print("   ⚠️ No API key provided, skipping API test")

        await client.close()
        return True

    except Exception as e:
        print(f"❌ Requesty client test failed: {e}")
        return False


async def test_simple_embedder():
    """Test simple embedder."""
    print("\n🔢 Testing Simple Embedder...")

    try:
        embedder = create_simple_embedder()
        print("✅ Simple embedder created successfully")

        # Test embedding generation
        test_text = "This is a test sentence for embedding."
        embedding = await embedder.embed_text(test_text)

        print(f"   Generated embedding dimension: {len(embedding)}")
        print(f"   Sample values: {embedding[:5]}...")
        print("✅ Embedding generation works")

        return True

    except Exception as e:
        print(f"❌ Simple embedder test failed: {e}")
        return False


def test_document_processor():
    """Test document processor."""
    print("\n📄 Testing Document Processor...")

    try:
        from core.document_processor import DocumentProcessor

        processor = DocumentProcessor(chunk_size=500, chunk_overlap=100)
        print("✅ Document processor created successfully")

        # Test with documents folder
        docs_path = Path("./documents")
        if docs_path.exists():
            processed_docs = processor.process_directory(str(docs_path))
            print(f"   Found {len(processed_docs)} documents to process")

            if processed_docs:
                doc = processed_docs[0]
                print(f"   Sample document: {doc.source_file}")
                print(f"   Chunks: {doc.total_chunks}")
                print(f"   Type: {doc.document_type}")
                print("✅ Document processing works")
            else:
                print("   ⚠️ No documents found in ./documents folder")
        else:
            print("   ⚠️ ./documents folder not found")

        return True

    except Exception as e:
        print(f"❌ Document processor test failed: {e}")
        return False


def test_schema_adapter():
    """Test schema adapter."""
    print("\n📋 Testing Schema Adapter...")

    try:
        from core.schema_adapter import SchemaAdapter

        adapter = SchemaAdapter()
        print("✅ Schema adapter created successfully")

        entity_types = adapter.get_supported_entity_types()
        relationship_types = adapter.get_supported_relationship_types()

        print(f"   Entity types: {len(entity_types)}")
        print(f"   Relationship types: {len(relationship_types)}")
        print(f"   Sample entities: {entity_types[:3]}")
        print(f"   Sample relationships: {relationship_types[:3]}")
        print("✅ Schema adapter works")

        return True

    except Exception as e:
        print(f"❌ Schema adapter test failed: {e}")
        return False


async def test_processor_initialization():
    """Test processor initialization."""
    print("\n🚀 Testing Processor Initialization...")

    try:
        from core.processor import GraphitiEnterpriseProcessor

        config = EnterpriseKGConfig()

        processor = GraphitiEnterpriseProcessor(
            neo4j_uri=config.neo4j.uri,
            neo4j_user=config.neo4j.user,
            neo4j_password=config.neo4j.password,
            chunk_size=500,
            chunk_overlap=100
        )
        print("✅ Processor created successfully")

        # Test initialization (this will try to connect to Neo4j)
        try:
            await processor.initialize()
            print("✅ Processor initialized successfully")

            # Test schema summary
            schema_summary = await processor.get_schema_summary()
            print(f"   Schema loaded: {schema_summary['total_entity_types']} entity types")

            await processor.close()
            return True

        except Exception as e:
            print(f"   ⚠️ Processor initialization failed: {e}")
            print("   This is likely due to Neo4j connection issues")
            return False

    except Exception as e:
        print(f"❌ Processor test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🧪 Enterprise Knowledge Graph - Setup Tests")
    print("=" * 50)

    tests = [
        ("Configuration", test_configuration, False),
        ("Requesty Client", test_requesty_client, True),
        ("Simple Embedder", test_simple_embedder, True),
        ("Document Processor", test_document_processor, False),
        ("Schema Adapter", test_schema_adapter, False),
        ("Processor Initialization", test_processor_initialization, True)
    ]

    results = []

    for test_name, test_func, is_async in tests:
        try:
            if is_async:
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")

    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1

    print(f"\nOverall: {passed}/{len(results)} tests passed")

    if passed == len(results):
        print("\n🎉 All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Run: python main.py")
        print("2. Or run: python end_to_end_flow.py")
    else:
        print("\n⚠️ Some tests failed. Please check your configuration.")
        print("Make sure you have:")
        print("1. Neo4j running and accessible")
        print("2. Correct environment variables set")
        print("3. Required dependencies installed")


if __name__ == "__main__":
    asyncio.run(main())
