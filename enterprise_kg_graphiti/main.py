"""
Main runner for Enterprise Knowledge Graph with Graphiti

This script provides a command-line interface for processing documents
and building knowledge graphs using Graphiti with enterprise schema compliance.
"""

import asyncio
import argparse
import logging
import sys
from pathlib import Path
from typing import List, Optional

from config import EnterpriseKGConfig, validate_config
from core.processor import GraphitiEnterpriseProcessor
from utils.helpers import (
    setup_logging,
    validate_directory_path,
    validate_file_path,
    format_processing_results,
    create_sample_documents,
    print_banner,
    print_config_summary,
    estimate_processing_time,
    get_file_info
)

logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Enterprise Knowledge Graph with Graphiti",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Process documents in current directory
    python main.py --documents ./documents

    # Process a single file
    python main.py --file document.pdf

    # Create sample documents and process them
    python main.py --create-samples --documents ./sample_docs

    # Use custom chunk size and search after processing
    python main.py --documents ./docs --chunk-size 1500 --search "project management"

    # Process with specific file types only
    python main.py --documents ./docs --extensions .pdf .docx
        """
    )

    # Input options
    group = parser.add_mutually_exclusive_group(required=False)
    group.add_argument(
        "--file", "-f",
        help="Process a single file"
    )
    group.add_argument(
        "--documents", "-d",
        default="./documents",
        help="Process all documents in a directory (default: ./documents)"
    )
    group.add_argument(
        "--create-samples",
        action="store_true",
        help="Create sample documents for testing"
    )

    # Processing options
    parser.add_argument(
        "--extensions",
        nargs="+",
        default=[".txt", ".md", ".json", ".docx", ".pdf"],
        help="File extensions to process (default: .txt .md .json .docx .pdf)"
    )

    parser.add_argument(
        "--document-type",
        choices=["report", "proposal", "contract", "policy", "meeting_notes", "structured_data"],
        help="Override document type for all files"
    )

    parser.add_argument(
        "--chunk-size",
        type=int,
        default=1000,
        help="Document chunk size in characters (default: 1000)"
    )

    parser.add_argument(
        "--chunk-overlap",
        type=int,
        default=200,
        help="Overlap between chunks in characters (default: 200)"
    )

    # Search options
    parser.add_argument(
        "--search", "-s",
        help="Perform a search query after processing"
    )

    parser.add_argument(
        "--search-limit",
        type=int,
        default=10,
        help="Maximum number of search results (default: 10)"
    )

    parser.add_argument(
        "--search-type",
        choices=["hybrid", "nodes", "edges"],
        default="hybrid",
        help="Type of search to perform (default: hybrid)"
    )

    # Output options
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )

    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress non-essential output"
    )

    parser.add_argument(
        "--output-dir",
        help="Directory to save sample documents (used with --create-samples)"
    )

    # Configuration options
    parser.add_argument(
        "--config-check",
        action="store_true",
        help="Check configuration and exit"
    )

    parser.add_argument(
        "--schema-info",
        action="store_true",
        help="Show schema information and exit"
    )

    return parser.parse_args()


async def main():
    """Main function."""
    args = parse_arguments()

    # Set up logging
    log_level = "DEBUG" if args.verbose else "WARNING" if args.quiet else "INFO"
    setup_logging(log_level)

    if not args.quiet:
        print_banner("Enterprise Knowledge Graph with Graphiti")

    # Load and validate configuration
    config = EnterpriseKGConfig()

    # Override config with command line arguments
    if args.chunk_size:
        config.processing.chunk_size = args.chunk_size
    if args.chunk_overlap:
        config.processing.chunk_overlap = args.chunk_overlap
    if args.extensions:
        config.processing.supported_extensions = args.extensions

    # Check configuration
    if args.config_check:
        print("\nConfiguration Check:")
        print("-" * 40)

        errors = validate_config()
        if errors:
            print("❌ Configuration errors found:")
            for error in errors:
                print(f"  - {error}")
            return 1
        else:
            print("✅ Configuration is valid")

        if not args.quiet:
            print_config_summary(config.to_dict())
        return 0

    # Validate configuration
    errors = validate_config()
    if errors:
        print("❌ Configuration errors:")
        for error in errors:
            print(f"  - {error}")
        print("\nPlease check your environment variables and try again.")
        return 1

    # Create sample documents if requested
    if args.create_samples:
        output_dir = args.output_dir or "./sample_documents"
        print(f"📁 Creating sample documents in {output_dir}...")

        try:
            created_files = create_sample_documents(output_dir)
            print(f"✅ Created {len(created_files)} sample documents:")
            for file_path in created_files:
                print(f"  - {file_path}")

            if not args.documents:
                print(f"\n💡 To process these documents, run:")
                print(f"python main.py --documents {output_dir}")
                return 0
            else:
                # Continue processing the created documents
                args.documents = output_dir

        except Exception as e:
            print(f"❌ Failed to create sample documents: {e}")
            return 1

    # Initialize processor
    try:
        print("🚀 Initializing Graphiti Enterprise Processor...")
        processor = GraphitiEnterpriseProcessor(
            neo4j_uri=config.neo4j.uri,
            neo4j_user=config.neo4j.user,
            neo4j_password=config.neo4j.password,
            chunk_size=config.processing.chunk_size,
            chunk_overlap=config.processing.chunk_overlap,
            store_raw_content=config.processing.store_raw_content
        )

        await processor.initialize()
        print("✅ Processor initialized successfully")

        # Show schema information if requested
        if args.schema_info:
            schema_summary = await processor.get_schema_summary()
            print("\nSchema Information:")
            print("-" * 40)
            print(f"Entity Types ({schema_summary['total_entity_types']}):")
            for entity_type in schema_summary['entity_types'][:10]:  # Show first 10
                print(f"  - {entity_type}")
            if schema_summary['total_entity_types'] > 10:
                print(f"  ... and {schema_summary['total_entity_types'] - 10} more")

            print(f"\nRelationship Types ({schema_summary['total_relationship_types']}):")
            for rel_type in schema_summary['relationship_types'][:10]:  # Show first 10
                print(f"  - {rel_type}")
            if schema_summary['total_relationship_types'] > 10:
                print(f"  ... and {schema_summary['total_relationship_types'] - 10} more")

            await processor.close()
            return 0

    except Exception as e:
        print(f"❌ Failed to initialize processor: {e}")
        return 1

    # Process documents
    try:
        results = []

        if args.file:
            # Process single file
            if not validate_file_path(args.file):
                print(f"❌ File not found or not readable: {args.file}")
                await processor.close()
                return 1

            file_info = get_file_info(args.file)
            print(f"📄 Processing file: {args.file}")
            print(f"   Size: {file_info.get('size', 0)} bytes")
            print(f"   Type: {file_info.get('extension', 'unknown')}")

            result = await processor.process_file(args.file, args.document_type)
            results = [result]

        elif args.documents:
            # Process directory
            if not validate_directory_path(args.documents):
                print(f"❌ Directory not found or not readable: {args.documents}")
                await processor.close()
                return 1

            # Count files first
            doc_path = Path(args.documents)
            file_count = sum(1 for ext in args.extensions
                           for _ in doc_path.rglob(f"*{ext}"))

            if file_count == 0:
                print(f"❌ No files found with extensions {args.extensions} in {args.documents}")
                await processor.close()
                return 1

            print(f"📁 Processing {file_count} files from {args.documents}")
            print(f"   Extensions: {', '.join(args.extensions)}")

            # Estimate processing time
            avg_size = 10000  # Rough estimate
            estimated_time = estimate_processing_time(file_count, avg_size)
            print(f"   Estimated time: {estimated_time}")

            results = await processor.process_directory(
                args.documents,
                args.extensions,
                args.document_type
            )

        # Format and display results
        summary = format_processing_results(results)

        print("\n📊 Processing Results:")
        print("-" * 40)
        print(f"Total files: {summary['total_files']}")
        print(f"Successful: {summary['successful']}")
        print(f"Failed: {summary['failed']}")
        print(f"Success rate: {summary['success_rate']:.1f}%")
        print(f"Total episodes added: {summary['total_episodes']}")

        if summary['errors'] and not args.quiet:
            print("\n❌ Errors:")
            for error in summary['errors'][:5]:  # Show first 5 errors
                print(f"  - {error['file']}: {error['error']}")
            if len(summary['errors']) > 5:
                print(f"  ... and {len(summary['errors']) - 5} more errors")

        # Perform search if requested
        if args.search and summary['successful'] > 0:
            print(f"\n🔍 Searching for: '{args.search}'")
            search_results = await processor.search(
                query=args.search,
                limit=args.search_limit
            )

            if search_results:
                print(f"\nFound {len(search_results)} results:")
                print("-" * 40)
                for i, result in enumerate(search_results[:args.search_limit], 1):
                    print(f"{i}. {result.get('fact', result.get('name', 'Unknown'))}")
                    if result.get('type') == 'relationship':
                        print(f"   Type: {result.get('relationship_type', 'Unknown')}")
                    elif result.get('type') == 'node':
                        print(f"   Entity: {result.get('entity_type', 'Unknown')}")
                    print()
            else:
                print("No results found.")

    except KeyboardInterrupt:
        print("\n⚠️ Processing interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Processing failed: {e}")
        logger.exception("Detailed error:")
        return 1
    finally:
        await processor.close()

    print("\n✅ Processing completed successfully!")
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
