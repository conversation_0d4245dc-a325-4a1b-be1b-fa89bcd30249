"""
Search Interface for Enterprise Knowledge Graph

This module provides enterprise-focused search capabilities
that leverage Graphiti's search functionality with schema awareness.
"""

import logging
from typing import List, Dict, Any, Optional
from graphiti_core import Graphiti
from graphiti_core.search.search_config_recipes import (
    COMBINED_HYBRID_SEARCH_CROSS_ENCODER,
    NODE_HYBRID_SEARCH_RRF,
    EDGE_HYBRID_SEARCH_RRF
)
from graphiti_core.search.search_filters import SearchFilters

from .schema_adapter import SchemaAdapter

logger = logging.getLogger(__name__)


class SearchInterface:
    """
    Enterprise search interface for Graphiti knowledge graph.
    
    Provides schema-aware search capabilities with enterprise-specific
    filtering and result formatting.
    """
    
    def __init__(self, graphiti: Graphiti, schema_adapter: SchemaAdapter):
        """
        Initialize the search interface.
        
        Args:
            graphiti: Graphiti instance
            schema_adapter: Schema adapter for enterprise ontology
        """
        self.graphiti = graphiti
        self.schema_adapter = schema_adapter
    
    async def search(self, 
                    query: str, 
                    limit: int = 10,
                    entity_types: List[str] = None,
                    relationship_types: List[str] = None,
                    search_type: str = "hybrid") -> List[Dict[str, Any]]:
        """
        Perform a search on the knowledge graph.
        
        Args:
            query: Search query
            limit: Maximum number of results
            entity_types: Filter by specific entity types
            relationship_types: Filter by specific relationship types
            search_type: Type of search ("hybrid", "edges", "nodes")
            
        Returns:
            List of formatted search results
        """
        try:
            # Validate entity and relationship types
            if entity_types:
                entity_types = [et for et in entity_types 
                              if self.schema_adapter.validate_entity_type(et)]
            
            if relationship_types:
                relationship_types = [rt for rt in relationship_types 
                                    if self.schema_adapter.validate_relationship_type(rt)]
            
            # Create search filters
            filters = SearchFilters()
            if entity_types:
                filters.entity_types = entity_types
            if relationship_types:
                filters.relationship_types = relationship_types
            
            # Perform search based on type
            if search_type == "nodes":
                results = await self._search_nodes(query, limit, filters)
            elif search_type == "edges":
                results = await self._search_edges(query, limit, filters)
            else:  # hybrid
                results = await self._search_hybrid(query, limit, filters)
            
            return self._format_search_results(results, search_type)
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    async def _search_hybrid(self, query: str, limit: int, filters: SearchFilters):
        """Perform hybrid search combining nodes and edges."""
        try:
            # Use Graphiti's standard search method
            results = await self.graphiti.search(query, limit=limit)
            return results
        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return []
    
    async def _search_nodes(self, query: str, limit: int, filters: SearchFilters):
        """Search for nodes specifically."""
        try:
            config = NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
            config.limit = limit
            
            results = await self.graphiti._search(
                query=query,
                config=config,
                filters=filters
            )
            return results
        except Exception as e:
            logger.error(f"Node search failed: {e}")
            return []
    
    async def _search_edges(self, query: str, limit: int, filters: SearchFilters):
        """Search for edges specifically."""
        try:
            config = EDGE_HYBRID_SEARCH_RRF.model_copy(deep=True)
            config.limit = limit
            
            results = await self.graphiti._search(
                query=query,
                config=config,
                filters=filters
            )
            return results
        except Exception as e:
            logger.error(f"Edge search failed: {e}")
            return []
    
    def _format_search_results(self, results, search_type: str) -> List[Dict[str, Any]]:
        """Format search results for enterprise use."""
        formatted_results = []
        
        try:
            if search_type == "nodes" and hasattr(results, 'nodes'):
                # Format node results
                for node in results.nodes:
                    formatted_results.append({
                        'type': 'node',
                        'uuid': node.uuid,
                        'name': node.name,
                        'summary': node.summary,
                        'labels': node.labels,
                        'created_at': node.created_at,
                        'attributes': getattr(node, 'attributes', {}),
                        'entity_type': node.labels[0] if node.labels else 'Unknown'
                    })
            
            elif search_type == "edges" and hasattr(results, 'edges'):
                # Format edge results
                for edge in results.edges:
                    formatted_results.append({
                        'type': 'edge',
                        'uuid': edge.uuid,
                        'fact': edge.fact,
                        'source_node_uuid': edge.source_node_uuid,
                        'target_node_uuid': edge.target_node_uuid,
                        'created_at': edge.created_at,
                        'valid_at': getattr(edge, 'valid_at', None),
                        'invalid_at': getattr(edge, 'invalid_at', None),
                        'relationship_type': getattr(edge, 'relationship_type', 'Unknown')
                    })
            
            else:
                # Format hybrid results (typically edges)
                for result in results:
                    formatted_results.append({
                        'type': 'relationship',
                        'uuid': result.uuid,
                        'fact': result.fact,
                        'source_node_uuid': getattr(result, 'source_node_uuid', None),
                        'target_node_uuid': getattr(result, 'target_node_uuid', None),
                        'created_at': getattr(result, 'created_at', None),
                        'valid_at': getattr(result, 'valid_at', None),
                        'invalid_at': getattr(result, 'invalid_at', None),
                        'relationship_type': getattr(result, 'relationship_type', 'Unknown')
                    })
        
        except Exception as e:
            logger.error(f"Error formatting search results: {e}")
        
        return formatted_results
    
    async def get_entity_relationships(self, entity_name: str) -> List[Dict[str, Any]]:
        """
        Get all relationships for a specific entity.
        
        Args:
            entity_name: Name of the entity
            
        Returns:
            List of relationships involving the entity
        """
        try:
            # Search for the entity first
            entity_results = await self.search(
                query=entity_name,
                search_type="nodes",
                limit=5
            )
            
            if not entity_results:
                return []
            
            # Get the most relevant entity
            target_entity = entity_results[0]
            entity_uuid = target_entity['uuid']
            
            # Search for relationships involving this entity
            relationship_query = f"relationships involving {entity_name}"
            relationship_results = await self.search(
                query=relationship_query,
                search_type="edges",
                limit=50
            )
            
            # Filter relationships that involve the target entity
            relevant_relationships = []
            for rel in relationship_results:
                if (rel.get('source_node_uuid') == entity_uuid or 
                    rel.get('target_node_uuid') == entity_uuid):
                    relevant_relationships.append(rel)
            
            return relevant_relationships
            
        except Exception as e:
            logger.error(f"Error getting entity relationships: {e}")
            return []
    
    async def search_by_entity_type(self, entity_type: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Search for all entities of a specific type.
        
        Args:
            entity_type: Type of entity to search for
            limit: Maximum number of results
            
        Returns:
            List of entities of the specified type
        """
        try:
            if not self.schema_adapter.validate_entity_type(entity_type):
                logger.warning(f"Invalid entity type: {entity_type}")
                return []
            
            results = await self.search(
                query=f"entities of type {entity_type}",
                entity_types=[entity_type],
                search_type="nodes",
                limit=limit
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching by entity type: {e}")
            return []
    
    async def search_by_relationship_type(self, relationship_type: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Search for all relationships of a specific type.
        
        Args:
            relationship_type: Type of relationship to search for
            limit: Maximum number of results
            
        Returns:
            List of relationships of the specified type
        """
        try:
            if not self.schema_adapter.validate_relationship_type(relationship_type):
                logger.warning(f"Invalid relationship type: {relationship_type}")
                return []
            
            results = await self.search(
                query=f"relationships of type {relationship_type}",
                relationship_types=[relationship_type],
                search_type="edges",
                limit=limit
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching by relationship type: {e}")
            return []
    
    async def get_schema_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the knowledge graph schema usage.
        
        Returns:
            Dictionary with schema statistics
        """
        try:
            stats = {
                'supported_entity_types': self.schema_adapter.get_supported_entity_types(),
                'supported_relationship_types': self.schema_adapter.get_supported_relationship_types(),
                'entity_type_counts': {},
                'relationship_type_counts': {}
            }
            
            # Get counts for each entity type
            for entity_type in stats['supported_entity_types']:
                entities = await self.search_by_entity_type(entity_type, limit=1000)
                stats['entity_type_counts'][entity_type] = len(entities)
            
            # Get counts for each relationship type
            for rel_type in stats['supported_relationship_types']:
                relationships = await self.search_by_relationship_type(rel_type, limit=1000)
                stats['relationship_type_counts'][rel_type] = len(relationships)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting schema statistics: {e}")
            return {}
