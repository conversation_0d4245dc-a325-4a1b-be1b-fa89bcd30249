"""
Requesty API Client for Enterprise Knowledge Graph

This module provides a custom LLM client that integrates with Requesty API
for use with Graphiti's knowledge graph system.
"""

import os
import logging
import json
from typing import Any, Dict, List, Optional, Union
import httpx
from pydantic import BaseModel
from graphiti_core.llm_client.client import LLMClient
from graphiti_core.llm_client.config import LLMConfig, ModelSize, DEFAULT_MAX_TOKENS
from graphiti_core.prompts.models import Message

logger = logging.getLogger(__name__)


class RequestyClient(LLMClient):
    """
    Custom LLM client for Requesty API integration.

    This client provides compatibility with Graphiti's LLMClient interface
    while using Requesty's API endpoints.
    """

    def __init__(self,
                 config: LLMConfig = None,
                 cache: bool = False,
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None,
                 model: str = "anthropic/claude-3.5-sonnet",
                 temperature: float = 0.1,
                 max_tokens: int = 4000):
        """
        Initialize the Requesty client.

        Args:
            config: LLM configuration object (optional)
            cache: Whether to enable caching (not implemented for Requesty)
            api_key: Requesty API key (defaults to REQUESTY_API_KEY env var)
            base_url: Requesty base URL (defaults to REQUESTY_BASE_URL env var)
            model: Model name to use
            temperature: Temperature for generation
            max_tokens: Maximum tokens for generation
        """
        # Create config if not provided
        if config is None:
            config = LLMConfig(
                model=model,
                temperature=temperature,
                max_tokens=max_tokens
            )

        # Initialize parent class
        super().__init__(config, cache)

        # Set Requesty-specific attributes
        self.api_key = api_key or os.getenv('REQUESTY_API_KEY')
        self.base_url = base_url or os.getenv('REQUESTY_BASE_URL', 'https://router.requesty.ai/v1')

        if not self.api_key:
            raise ValueError("Requesty API key is required. Set REQUESTY_API_KEY environment variable.")

        # Remove trailing slash from base_url
        self.base_url = self.base_url.rstrip('/')

        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            headers={
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            },
            timeout=60.0
        )

        logger.info(f"Initialized Requesty client with model: {self.model}")

    async def _generate_response(self,
                               messages: List[Message],
                               response_model: Optional[BaseModel] = None,
                               max_tokens: int = DEFAULT_MAX_TOKENS,
                               model_size: ModelSize = ModelSize.medium) -> Dict[str, Any]:
        """
        Generate a response using Requesty API (required by LLMClient).

        Args:
            messages: List of Message objects
            response_model: Optional Pydantic model for structured output
            max_tokens: Maximum tokens to generate
            model_size: Model size (ignored for Requesty)

        Returns:
            Dictionary containing the response
        """
        try:
            # Convert Message objects to dict format for API
            api_messages = []
            for msg in messages:
                api_messages.append({
                    'role': msg.role,
                    'content': msg.content
                })

            # Prepare the request payload
            payload = {
                'model': self.model,
                'messages': api_messages,
                'temperature': self.temperature,
                'max_tokens': max_tokens
            }

            # Make the API request
            response = await self.client.post(
                f'{self.base_url}/chat/completions',
                json=payload
            )

            response.raise_for_status()
            result = response.json()

            # Extract the generated text
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']

                # If response_model is specified, try to parse as JSON
                if response_model:
                    try:
                        parsed_content = json.loads(content)
                        return parsed_content
                    except json.JSONDecodeError:
                        # Try to extract JSON from the content
                        import re
                        json_match = re.search(r'\{.*\}', content, re.DOTALL)
                        if json_match:
                            return json.loads(json_match.group())
                        else:
                            # Return as text if JSON parsing fails
                            return {"content": content}
                else:
                    # Return as text for non-structured responses
                    return {"content": content}
            else:
                raise ValueError("No response generated from Requesty API")

        except httpx.HTTPStatusError as e:
            logger.error(f"Requesty API HTTP error: {e.response.status_code} - {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Error generating response with Requesty: {e}")
            raise



    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()

    def __del__(self):
        """Cleanup when object is destroyed."""
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.create_task(self.close())
        except:
            pass


class RequestyConfig(LLMConfig):
    """Configuration for Requesty client."""

    def __init__(self,
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None,
                 model: str = "anthropic/claude-3.5-sonnet",
                 temperature: float = 0.1,
                 max_tokens: int = 4000):
        self.api_key = api_key or os.getenv('REQUESTY_API_KEY')
        self.base_url = base_url or os.getenv('REQUESTY_BASE_URL', 'https://router.requesty.ai/v1')
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens


def create_requesty_client() -> RequestyClient:
    """
    Factory function to create a Requesty client with environment configuration.

    Returns:
        Configured RequestyClient instance
    """
    # Create LLM config
    config = LLMConfig(
        model=os.getenv('LLM_MODEL', 'anthropic/claude-3.5-sonnet'),
        temperature=float(os.getenv('LLM_TEMPERATURE', '0.1')),
        max_tokens=int(os.getenv('LLM_MAX_TOKENS', '4000'))
    )

    return RequestyClient(
        config=config,
        api_key=os.getenv('REQUESTY_API_KEY'),
        base_url=os.getenv('REQUESTY_BASE_URL')
    )
