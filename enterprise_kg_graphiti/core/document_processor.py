"""
Document Processor for Enterprise Knowledge Graph

This module handles document processing, chunking, and preparation
for knowledge graph extraction using Graphiti.
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional, Union, Iterator
from dataclasses import dataclass
from pathlib import Path
import hashlib

# Document processing libraries
try:
    import docx
    from docx import Document as DocxDocument
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class DocumentChunk:
    """Represents a chunk of document content."""
    chunk_id: str
    text: str
    start_position: int
    end_position: int
    chunk_index: int
    source_file: str
    metadata: Dict[str, Any]


@dataclass
class ProcessedDocument:
    """Represents a processed document with chunks."""
    document_id: str
    source_file: str
    document_type: str
    total_chunks: int
    chunks: List[DocumentChunk]
    metadata: Dict[str, Any]


class DocumentProcessor:
    """
    Processes various document types and prepares them for knowledge graph extraction.
    
    Supports:
    - Text files (.txt, .md)
    - JSON files (.json)
    - Word documents (.docx) - if python-docx is available
    - PDF files (.pdf) - if PyPDF2 is available
    """
    
    def __init__(self, 
                 chunk_size: int = 1000,
                 chunk_overlap: int = 200,
                 min_chunk_size: int = 100):
        """
        Initialize the document processor.
        
        Args:
            chunk_size: Maximum size of each chunk in characters
            chunk_overlap: Number of characters to overlap between chunks
            min_chunk_size: Minimum size for a chunk to be considered valid
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.min_chunk_size = min_chunk_size
        
        # Check available libraries
        if not DOCX_AVAILABLE:
            logger.warning("python-docx not available. DOCX files will not be processed.")
        if not PDF_AVAILABLE:
            logger.warning("PyPDF2 not available. PDF files will not be processed.")
    
    def process_file(self, file_path: str) -> Optional[ProcessedDocument]:
        """
        Process a single file and return chunks.
        
        Args:
            file_path: Path to the file to process
            
        Returns:
            ProcessedDocument with chunks, or None if processing failed
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.error(f"File not found: {file_path}")
                return None
            
            # Determine file type and extract content
            content = self._extract_content(file_path)
            if not content:
                logger.warning(f"No content extracted from {file_path}")
                return None
            
            # Create document ID
            document_id = self._generate_document_id(file_path)
            
            # Create chunks
            chunks = self._create_chunks(content, str(file_path), document_id)
            
            # Determine document type
            document_type = self._determine_document_type(file_path, content)
            
            return ProcessedDocument(
                document_id=document_id,
                source_file=str(file_path),
                document_type=document_type,
                total_chunks=len(chunks),
                chunks=chunks,
                metadata={
                    'file_size': file_path.stat().st_size,
                    'file_extension': file_path.suffix,
                    'processing_timestamp': None  # Will be set by processor
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            return None
    
    def process_directory(self, 
                         directory_path: str, 
                         file_patterns: List[str] = None) -> List[ProcessedDocument]:
        """
        Process all files in a directory.
        
        Args:
            directory_path: Path to directory containing files
            file_patterns: List of file extensions to process (e.g., ['.txt', '.pdf'])
            
        Returns:
            List of ProcessedDocument objects
        """
        if file_patterns is None:
            file_patterns = ['.txt', '.md', '.json', '.docx', '.pdf']
        
        directory_path = Path(directory_path)
        processed_docs = []
        
        if not directory_path.exists():
            logger.error(f"Directory not found: {directory_path}")
            return processed_docs
        
        # Find all matching files
        for pattern in file_patterns:
            for file_path in directory_path.rglob(f"*{pattern}"):
                if file_path.is_file():
                    doc = self.process_file(str(file_path))
                    if doc:
                        processed_docs.append(doc)
        
        logger.info(f"Processed {len(processed_docs)} documents from {directory_path}")
        return processed_docs
    
    def _extract_content(self, file_path: Path) -> Optional[str]:
        """Extract text content from a file based on its type."""
        try:
            suffix = file_path.suffix.lower()
            
            if suffix in ['.txt', '.md']:
                return self._extract_text_content(file_path)
            elif suffix == '.json':
                return self._extract_json_content(file_path)
            elif suffix == '.docx' and DOCX_AVAILABLE:
                return self._extract_docx_content(file_path)
            elif suffix == '.pdf' and PDF_AVAILABLE:
                return self._extract_pdf_content(file_path)
            else:
                logger.warning(f"Unsupported file type: {suffix}")
                return None
                
        except Exception as e:
            logger.error(f"Error extracting content from {file_path}: {e}")
            return None
    
    def _extract_text_content(self, file_path: Path) -> str:
        """Extract content from text files."""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def _extract_json_content(self, file_path: Path) -> str:
        """Extract content from JSON files."""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # Convert JSON to readable text format
            return json.dumps(data, indent=2, ensure_ascii=False)
    
    def _extract_docx_content(self, file_path: Path) -> str:
        """Extract content from DOCX files."""
        doc = DocxDocument(file_path)
        paragraphs = []
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                paragraphs.append(paragraph.text)
        return '\n'.join(paragraphs)
    
    def _extract_pdf_content(self, file_path: Path) -> str:
        """Extract content from PDF files."""
        with open(file_path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            text_content = []
            for page in reader.pages:
                text_content.append(page.extract_text())
            return '\n'.join(text_content)
    
    def _create_chunks(self, content: str, source_file: str, document_id: str) -> List[DocumentChunk]:
        """Create chunks from document content."""
        chunks = []
        
        if len(content) <= self.chunk_size:
            # Document is small enough to be a single chunk
            chunk = DocumentChunk(
                chunk_id=f"{document_id}_chunk_0",
                text=content,
                start_position=0,
                end_position=len(content),
                chunk_index=0,
                source_file=source_file,
                metadata={}
            )
            chunks.append(chunk)
            return chunks
        
        # Split into overlapping chunks
        start = 0
        chunk_index = 0
        
        while start < len(content):
            end = min(start + self.chunk_size, len(content))
            
            # Try to break at word boundaries
            if end < len(content):
                # Look for the last space within the chunk
                last_space = content.rfind(' ', start, end)
                if last_space > start:
                    end = last_space
            
            chunk_text = content[start:end].strip()
            
            # Only create chunk if it meets minimum size requirement
            if len(chunk_text) >= self.min_chunk_size:
                chunk = DocumentChunk(
                    chunk_id=f"{document_id}_chunk_{chunk_index}",
                    text=chunk_text,
                    start_position=start,
                    end_position=end,
                    chunk_index=chunk_index,
                    source_file=source_file,
                    metadata={}
                )
                chunks.append(chunk)
                chunk_index += 1
            
            # Move start position with overlap
            start = max(start + self.chunk_size - self.chunk_overlap, end)
        
        return chunks
    
    def _generate_document_id(self, file_path: Path) -> str:
        """Generate a unique document ID based on file path and content."""
        # Use file path and modification time for ID
        path_str = str(file_path.absolute())
        mtime = str(file_path.stat().st_mtime)
        combined = f"{path_str}_{mtime}"
        return hashlib.md5(combined.encode()).hexdigest()
    
    def _determine_document_type(self, file_path: Path, content: str) -> str:
        """Determine the document type based on file and content analysis."""
        suffix = file_path.suffix.lower()
        filename = file_path.name.lower()
        
        # Check filename patterns
        if any(keyword in filename for keyword in ['report', 'analysis']):
            return 'report'
        elif any(keyword in filename for keyword in ['proposal', 'rfp']):
            return 'proposal'
        elif any(keyword in filename for keyword in ['contract', 'agreement']):
            return 'contract'
        elif any(keyword in filename for keyword in ['policy', 'procedure']):
            return 'policy'
        elif any(keyword in filename for keyword in ['meeting', 'notes', 'minutes']):
            return 'meeting_notes'
        elif suffix == '.json':
            return 'structured_data'
        else:
            return 'document'
