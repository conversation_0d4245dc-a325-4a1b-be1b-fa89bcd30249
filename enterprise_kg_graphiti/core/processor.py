"""
Main Graphiti Enterprise Processor

This module provides the main processor class that integrates Graphiti
with enterprise schema definitions for knowledge graph creation.
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Union
from pathlib import Path

from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType

from .schema_adapter import SchemaAdapter
from .document_processor import DocumentProcessor, ProcessedDocument, DocumentChunk
from .search_interface import SearchInterface
from .requesty_client import create_requesty_client
from .simple_embedder import create_simple_embedder

logger = logging.getLogger(__name__)


class GraphitiEnterpriseProcessor:
    """
    Main processor for enterprise knowledge graph creation using Graphiti.

    This class integrates Graphiti's capabilities with enterprise schema
    definitions to provide a complete knowledge graph solution.
    """

    def __init__(self,
                 neo4j_uri: str,
                 neo4j_user: str,
                 neo4j_password: str,
                 llm_client=None,
                 embedder=None,
                 cross_encoder=None,
                 chunk_size: int = 1000,
                 chunk_overlap: int = 200,
                 store_raw_content: bool = True):
        """
        Initialize the Graphiti Enterprise Processor.

        Args:
            neo4j_uri: Neo4j database URI
            neo4j_user: Neo4j username
            neo4j_password: Neo4j password
            llm_client: Optional LLM client for Graphiti (defaults to Requesty)
            embedder: Optional embedder client for Graphiti (defaults to SimpleEmbedder)
            cross_encoder: Optional cross encoder client for Graphiti
            chunk_size: Size of document chunks in characters
            chunk_overlap: Overlap between chunks in characters
            store_raw_content: Whether to store raw episode content
        """
        # Use Requesty client if no LLM client provided
        if llm_client is None:
            llm_client = create_requesty_client()
            logger.info("Using Requesty API client for LLM")

        # Use simple embedder if no embedder provided
        if embedder is None:
            embedder = create_simple_embedder()
            logger.info("Using SimpleEmbedder for embeddings")

        # Initialize Graphiti
        self.graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password,
            llm_client=llm_client,
            embedder=embedder,
            cross_encoder=cross_encoder,
            store_raw_episode_content=store_raw_content
        )

        # Initialize components
        self.schema_adapter = SchemaAdapter()
        self.document_processor = DocumentProcessor(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        self.search_interface = SearchInterface(self.graphiti, self.schema_adapter)

        # Processing settings
        self.group_id = "enterprise_kg"
        self.update_communities = True

        logger.info("GraphitiEnterpriseProcessor initialized successfully")

    async def initialize(self):
        """Initialize the knowledge graph database."""
        try:
            await self.graphiti.build_indices_and_constraints()
            logger.info("Knowledge graph indices and constraints built successfully")
        except Exception as e:
            logger.error(f"Failed to initialize knowledge graph: {e}")
            raise

    async def close(self):
        """Close the connection to the knowledge graph."""
        await self.graphiti.close()
        logger.info("Knowledge graph connection closed")

    async def process_file(self, file_path: str, document_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a single file and add it to the knowledge graph.

        Args:
            file_path: Path to the file to process
            document_type: Optional document type override

        Returns:
            Dictionary with processing results
        """
        try:
            # Process the document
            processed_doc = self.document_processor.process_file(file_path)
            if not processed_doc:
                return {
                    'success': False,
                    'error': f'Failed to process file: {file_path}',
                    'episodes_added': 0
                }

            # Override document type if provided
            if document_type:
                processed_doc.document_type = document_type

            # Add chunks as episodes to Graphiti
            episodes_added = 0
            for chunk in processed_doc.chunks:
                try:
                    result = await self._add_chunk_as_episode(chunk, processed_doc)
                    if result:
                        episodes_added += 1
                except Exception as e:
                    logger.error(f"Failed to add chunk {chunk.chunk_id}: {e}")

            return {
                'success': True,
                'document_id': processed_doc.document_id,
                'total_chunks': processed_doc.total_chunks,
                'episodes_added': episodes_added,
                'document_type': processed_doc.document_type
            }

        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'episodes_added': 0
            }

    async def process_directory(self,
                              directory_path: str,
                              file_patterns: List[str] = None,
                              document_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Process all files in a directory.

        Args:
            directory_path: Path to directory containing files
            file_patterns: List of file extensions to process
            document_type: Optional document type override for all files

        Returns:
            List of processing results for each file
        """
        try:
            # Process all documents
            processed_docs = self.document_processor.process_directory(
                directory_path, file_patterns
            )

            if not processed_docs:
                logger.warning(f"No documents found in {directory_path}")
                return []

            # Process each document
            results = []
            for processed_doc in processed_docs:
                try:
                    # Override document type if provided
                    if document_type:
                        processed_doc.document_type = document_type

                    # Add chunks as episodes
                    episodes_added = 0
                    for chunk in processed_doc.chunks:
                        try:
                            result = await self._add_chunk_as_episode(chunk, processed_doc)
                            if result:
                                episodes_added += 1
                        except Exception as e:
                            logger.error(f"Failed to add chunk {chunk.chunk_id}: {e}")

                    results.append({
                        'success': True,
                        'file_path': processed_doc.source_file,
                        'document_id': processed_doc.document_id,
                        'total_chunks': processed_doc.total_chunks,
                        'episodes_added': episodes_added,
                        'document_type': processed_doc.document_type
                    })

                except Exception as e:
                    logger.error(f"Error processing document {processed_doc.source_file}: {e}")
                    results.append({
                        'success': False,
                        'file_path': processed_doc.source_file,
                        'error': str(e),
                        'episodes_added': 0
                    })

            return results

        except Exception as e:
            logger.error(f"Error processing directory {directory_path}: {e}")
            return []

    async def _add_chunk_as_episode(self,
                                   chunk: DocumentChunk,
                                   document: ProcessedDocument) -> bool:
        """
        Add a document chunk as an episode to Graphiti and create file source relationships.

        Args:
            chunk: Document chunk to add
            document: Parent document information

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create episode name
            episode_name = f"{Path(document.source_file).stem}_chunk_{chunk.chunk_index}"

            # Create source description
            source_description = (
                f"Document chunk from {document.source_file} "
                f"(type: {document.document_type}, chunk {chunk.chunk_index + 1}/{document.total_chunks})"
            )

            # Add episode to Graphiti (using default entity extraction for now)
            episode_result = await self.graphiti.add_episode(
                name=episode_name,
                episode_body=chunk.text,
                source_description=source_description,
                reference_time=datetime.now(timezone.utc),
                source=EpisodeType.text,
                group_id=self.group_id,
                update_communities=self.update_communities
            )

            # Create file source node and relationships
            await self._create_file_source_relationships(document, episode_result)

            logger.debug(f"Added episode: {episode_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to add chunk as episode: {e}")
            return False

    async def _create_file_source_relationships(self, document: ProcessedDocument, episode_result) -> None:
        """
        Create file source node and 'contains' relationships with extracted entities.

        Args:
            document: Document information
            episode_result: Result from adding episode to Graphiti
        """
        try:
            # Create file source node using Neo4j driver directly
            driver = self.graphiti.driver

            file_name = Path(document.source_file).name
            file_path = str(Path(document.source_file).absolute())

            # Create or update file source node
            async with driver.session() as session:
                # Create file source node
                file_query = """
                MERGE (f:FileSource {path: $file_path})
                SET f.name = $file_name,
                    f.document_type = $document_type,
                    f.total_chunks = $total_chunks,
                    f.document_id = $document_id,
                    f.created_at = datetime(),
                    f.updated_at = datetime()
                RETURN f
                """

                await session.run(file_query, {
                    'file_path': file_path,
                    'file_name': file_name,
                    'document_type': document.document_type,
                    'total_chunks': document.total_chunks,
                    'document_id': document.document_id
                })

                # Create 'contains' relationships with extracted entities
                if hasattr(episode_result, 'nodes') and episode_result.nodes:
                    for node in episode_result.nodes:
                        contains_query = """
                        MATCH (f:FileSource {path: $file_path})
                        MATCH (e:Entity {uuid: $entity_uuid})
                        MERGE (f)-[r:CONTAINS]->(e)
                        SET r.created_at = datetime(),
                            r.extraction_source = 'graphiti_enterprise'
                        """

                        await session.run(contains_query, {
                            'file_path': file_path,
                            'entity_uuid': node.uuid
                        })

                # Create relationship with episode
                episode_query = """
                MATCH (f:FileSource {path: $file_path})
                MATCH (ep:EpisodicNode {uuid: $episode_uuid})
                MERGE (f)-[r:CONTAINS]->(ep)
                SET r.created_at = datetime(),
                    r.relationship_type = 'file_contains_episode'
                """

                await session.run(episode_query, {
                    'file_path': file_path,
                    'episode_uuid': episode_result.episode.uuid
                })

            logger.debug(f"Created file source relationships for {file_name}")

        except Exception as e:
            logger.error(f"Failed to create file source relationships: {e}")
            # Don't raise exception as this is supplementary functionality

    async def search(self,
                    query: str,
                    limit: int = 10,
                    entity_types: List[str] = None,
                    relationship_types: List[str] = None) -> List[Dict[str, Any]]:
        """
        Search the knowledge graph.

        Args:
            query: Search query
            limit: Maximum number of results
            entity_types: Filter by entity types
            relationship_types: Filter by relationship types

        Returns:
            List of search results
        """
        return await self.search_interface.search(
            query=query,
            limit=limit,
            entity_types=entity_types,
            relationship_types=relationship_types
        )

    async def get_entity_relationships(self, entity_name: str) -> List[Dict[str, Any]]:
        """
        Get all relationships for a specific entity.

        Args:
            entity_name: Name of the entity

        Returns:
            List of relationships
        """
        return await self.search_interface.get_entity_relationships(entity_name)

    async def get_schema_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current schema.

        Returns:
            Dictionary with schema information
        """
        return {
            'entity_types': self.schema_adapter.get_supported_entity_types(),
            'relationship_types': self.schema_adapter.get_supported_relationship_types(),
            'total_entity_types': len(self.schema_adapter.get_supported_entity_types()),
            'total_relationship_types': len(self.schema_adapter.get_supported_relationship_types())
        }

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        Get processing statistics.

        Returns:
            Dictionary with processing statistics
        """
        return {
            'chunk_size': self.document_processor.chunk_size,
            'chunk_overlap': self.document_processor.chunk_overlap,
            'group_id': self.group_id,
            'update_communities': self.update_communities
        }
