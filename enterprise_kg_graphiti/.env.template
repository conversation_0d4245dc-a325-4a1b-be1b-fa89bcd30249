# Enterprise Knowledge Graph with Graphiti - Environment Configuration
# Copy this file to .env and fill in your actual values

# Neo4j Database Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password_here
NEO4J_DATABASE=neo4j

# Requesty API Configuration (required for LLM)
REQUESTY_API_KEY=your_requesty_api_key_here
REQUESTY_BASE_URL=https://router.requesty.ai/v1

# Optional: Alternative LLM Providers
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# GOOGLE_API_KEY=your_google_api_key_here

# LLM Configuration
LLM_PROVIDER=requesty
LLM_MODEL=anthropic/claude-3.5-sonnet
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=4000

# Embedder Configuration (using simple embedder - no API required)
EMBEDDER_PROVIDER=simple
EMBEDDER_MODEL=simple-embedder
EMBEDDING_DIM=384

# Document Processing Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MIN_CHUNK_SIZE=100

# Knowledge Graph Configuration
GROUP_ID=enterprise_kg
UPDATE_COMMUNITIES=true
STORE_RAW_CONTENT=true

# Search Configuration
SEARCH_DEFAULT_LIMIT=10
SEARCH_MAX_LIMIT=100
DEFAULT_SEARCH_TYPE=hybrid

# Logging Configuration
LOG_LEVEL=INFO
