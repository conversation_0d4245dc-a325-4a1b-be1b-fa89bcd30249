#!/usr/bin/env python3
"""
Test script for the minimal Enterprise KG package

This script verifies that all essential components work correctly
in the minimal standalone package.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all essential modules can be imported."""
    print("📦 Testing Essential Module Imports")
    print("=" * 40)
    
    essential_modules = [
        ("standalone_processor", "Standalone Processor"),
        ("storage.neo4j_client", "Neo4j Client"),
        ("constants.schemas", "Schema Definitions"),
        ("constants.entities", "Entity Constants"),
        ("constants.relationships", "Relationship Constants"),
        ("prompt_generator", "Prompt Generator"),
        ("utils.helpers", "Helper Functions"),
    ]
    
    all_good = True
    
    for module_name, description in essential_modules:
        try:
            __import__(module_name)
            print(f"✓ {description}")
        except ImportError as e:
            print(f"✗ {description}: {e}")
            all_good = False
    
    print()
    return all_good

def test_constants():
    """Test that constants are properly defined."""
    print("🔧 Testing Constants")
    print("=" * 40)
    
    try:
        from constants.entities import get_all_entity_types
        from constants.relationships import get_all_relationship_types
        from constants.schemas import EntityRelationship, DocumentSummary
        
        entity_types = get_all_entity_types()
        relationship_types = get_all_relationship_types()
        
        print(f"✓ Entity types loaded: {len(entity_types)} types")
        print(f"✓ Relationship types loaded: {len(relationship_types)} types")
        print(f"✓ Schema classes available: EntityRelationship, DocumentSummary")
        
        # Test creating a sample relationship
        sample_rel = EntityRelationship(
            subject="John Doe",
            predicate="involved_in",
            object="Project Alpha",
            confidence_score=0.9,
            context="Test relationship",
            source_sentence="John Doe is involved in Project Alpha."
        )
        print(f"✓ Sample relationship created: {sample_rel.subject} {sample_rel.predicate} {sample_rel.object}")
        
        print()
        return True
        
    except Exception as e:
        print(f"✗ Constants test failed: {e}")
        print()
        return False

def test_processor_creation():
    """Test that the processor can be created."""
    print("🤖 Testing Processor Creation")
    print("=" * 40)
    
    try:
        from standalone_processor import LLMClient, create_standalone_processor
        
        # Test LLM client creation (without actual API call)
        llm_client = LLMClient(provider="openai", model="gpt-4o", api_key="test-key")
        print(f"✓ LLM client created: {llm_client.provider}/{llm_client.model}")
        
        # Test processor creation (will fail on Neo4j connection, but that's expected)
        try:
            processor = create_standalone_processor(
                neo4j_uri="bolt://localhost:7687",
                neo4j_user="neo4j", 
                neo4j_password="test",
                llm_provider="openai",
                llm_model="gpt-4o",
                llm_api_key="test-key"
            )
            print("✓ Processor created successfully")
        except Exception as e:
            if "Failed to establish connection" in str(e) or "Neo4j" in str(e):
                print("✓ Processor creation works (Neo4j connection expected to fail in test)")
            else:
                print(f"✗ Processor creation failed: {e}")
                return False
        
        print()
        return True
        
    except Exception as e:
        print(f"✗ Processor test failed: {e}")
        print()
        return False

def test_prompt_generator():
    """Test prompt generation."""
    print("📝 Testing Prompt Generator")
    print("=" * 40)
    
    try:
        from prompt_generator import PromptGenerator, create_full_prompt_generator
        
        # Test basic prompt generator
        basic_generator = PromptGenerator(
            focus_entities=["Person", "Project"],
            focus_relationships=["involved_in", "manages"],
            use_all_constants=False
        )
        
        sample_content = "John Doe is working on Project Alpha and manages the development team."
        
        # Test relationship extraction prompt
        rel_prompt = basic_generator.generate_relationship_extraction_prompt(sample_content)
        print(f"✓ Relationship extraction prompt generated ({len(rel_prompt)} chars)")
        
        # Test summarization prompt
        sum_prompt = basic_generator.generate_summarization_prompt(sample_content)
        print(f"✓ Summarization prompt generated ({len(sum_prompt)} chars)")
        
        # Test full prompt generator
        full_generator = create_full_prompt_generator()
        full_prompt = full_generator.generate_relationship_extraction_prompt(sample_content)
        print(f"✓ Full prompt generator works ({len(full_prompt)} chars)")
        
        print()
        return True
        
    except Exception as e:
        print(f"✗ Prompt generator test failed: {e}")
        print()
        return False

def test_file_structure():
    """Test that all essential files are present."""
    print("📁 Testing File Structure")
    print("=" * 40)
    
    essential_files = [
        "main.py",
        "standalone_processor.py", 
        "prompt_generator.py",
        "test_config.py",
        ".env",
        "requirements_standalone.txt",
        "README.md",
        "ENV_SETUP.md",
        "constants/__init__.py",
        "constants/entities.py",
        "constants/relationships.py", 
        "constants/schemas.py",
        "storage/__init__.py",
        "storage/neo4j_client.py",
        "utils/__init__.py",
        "utils/helpers.py"
    ]
    
    missing_files = []
    
    for file_path in essential_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - MISSING")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  Missing {len(missing_files)} essential files")
        print()
        return False
    else:
        print(f"\n✓ All {len(essential_files)} essential files present")
        print()
        return True

def main():
    """Run all tests for the minimal package."""
    print("🚀 Enterprise KG Minimal Package Test")
    print("=" * 50)
    print()
    
    tests = [
        ("File Structure", test_file_structure),
        ("Module Imports", test_imports),
        ("Constants", test_constants),
        ("Prompt Generator", test_prompt_generator),
        ("Processor Creation", test_processor_creation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results[test_name] = False
        print()
    
    # Summary
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")
    
    print()
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The minimal package is ready to use.")
        print("\nNext steps:")
        print("1. Set NEO4J_PASSWORD in .env file")
        print("2. Run: python test_config.py")
        print("3. Run: python main.py --create-samples")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
