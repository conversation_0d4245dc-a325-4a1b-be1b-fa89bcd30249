#!/usr/bin/env python3
"""
Example queries demonstrating enhanced node properties for GraphRAG.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from storage.neo4j_client import Neo4jClient, Neo4jConnection


def create_neo4j_client():
    """Create Neo4j client from environment variables."""
    connection = Neo4jConnection(
        uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        user=os.getenv("NEO4J_USER", "neo4j"),
        password=os.getenv("NEO4J_PASSWORD", "password"),
        database=os.getenv("NEO4J_DATABASE")
    )
    return Neo4jClient(connection)


def run_enhanced_queries():
    """Run example queries that leverage enhanced properties."""
    client = create_neo4j_client()
    
    print("🔍 Enhanced Property Queries for GraphRAG")
    print("=" * 50)
    
    try:
        # Query 1: Find high-importance entities
        print("\n1. 🎯 High-Importance Entities (graph_importance > 0.8)")
        print("-" * 50)
        
        query1 = """
        MATCH (n)
        WHERE n.graph_importance > 0.8
        RETURN n.name, n.entity_type, n.graph_importance, n.category
        ORDER BY n.graph_importance DESC
        LIMIT 10
        """
        
        driver = client._get_driver()
        with driver.session(database=client.connection.database) as session:
            result = session.run(query1)
            for record in result:
                print(f"  • {record['n.name']} ({record['n.entity_type']}) - "
                      f"Importance: {record['n.graph_importance']:.2f}, "
                      f"Category: {record['n.category']}")
        
        # Query 2: Find leadership network
        print("\n2. 👑 Leadership Network (leadership_role = true)")
        print("-" * 50)
        
        query2 = """
        MATCH (leader)
        WHERE leader.leadership_role = true
        OPTIONAL MATCH (leader)-[r:MANAGES|LEADS|REPORTS_TO]-(other)
        RETURN leader.name, leader.entity_type, 
               collect(DISTINCT type(r)) as relationship_types,
               collect(DISTINCT other.name) as connected_entities
        """
        
        with driver.session(database=client.connection.database) as session:
            result = session.run(query2)
            for record in result:
                print(f"  • {record['leader.name']} ({record['leader.entity_type']})")
                if record['relationship_types']:
                    print(f"    Relationships: {', '.join(record['relationship_types'])}")
                if record['connected_entities']:
                    connected = [e for e in record['connected_entities'] if e]
                    if connected:
                        print(f"    Connected to: {', '.join(connected[:3])}...")
        
        # Query 3: Technology integration map
        print("\n3. 💻 Technology Integration Map (is_technology = true)")
        print("-" * 50)
        
        query3 = """
        MATCH (tech)
        WHERE tech.is_technology = true
        OPTIONAL MATCH (tech)-[r:INTEGRATES_WITH|RUNS_ON|ACCESSES]-(other)
        RETURN tech.name, tech.description,
               collect(DISTINCT type(r)) as integration_types,
               collect(DISTINCT other.name) as integrated_with
        LIMIT 5
        """
        
        with driver.session(database=client.connection.database) as session:
            result = session.run(query3)
            for record in result:
                print(f"  • {record['tech.name']}")
                print(f"    Description: {record['tech.description']}")
                if record['integration_types']:
                    print(f"    Integration types: {', '.join(record['integration_types'])}")
                if record['integrated_with']:
                    integrated = [e for e in record['integrated_with'] if e]
                    if integrated:
                        print(f"    Integrated with: {', '.join(integrated[:2])}...")
        
        # Query 4: Knowledge sources by category
        print("\n4. 📚 Knowledge Sources by Category (contains_knowledge = true)")
        print("-" * 50)
        
        query4 = """
        MATCH (doc)
        WHERE doc.contains_knowledge = true
        RETURN doc.category, 
               collect(doc.name) as documents,
               avg(doc.graph_importance) as avg_importance
        ORDER BY avg_importance DESC
        """
        
        with driver.session(database=client.connection.database) as session:
            result = session.run(query4)
            for record in result:
                print(f"  • Category: {record['doc.category']}")
                print(f"    Documents: {', '.join(record['documents'][:3])}...")
                print(f"    Avg Importance: {record['avg_importance']:.2f}")
        
        # Query 5: Semantic search example
        print("\n5. 🔍 Semantic Search (context_keywords contains 'manager')")
        print("-" * 50)
        
        query5 = """
        MATCH (n)
        WHERE any(keyword IN n.context_keywords WHERE keyword CONTAINS 'manager')
        RETURN n.name, n.entity_type, n.context_keywords
        LIMIT 5
        """
        
        with driver.session(database=client.connection.database) as session:
            result = session.run(query5)
            for record in result:
                print(f"  • {record['n.name']} ({record['n.entity_type']})")
                print(f"    Keywords: {', '.join(record['n.context_keywords'][:3])}...")
        
        # Query 6: Organizational structure
        print("\n6. 🏢 Organizational Structure (is_organization = true)")
        print("-" * 50)
        
        query6 = """
        MATCH (org)
        WHERE org.is_organization = true
        OPTIONAL MATCH (org)-[:CONTAINS|PART_OF]-(unit)
        WHERE unit.is_internal_unit = true
        RETURN org.name, org.category,
               collect(DISTINCT unit.name) as internal_units
        """
        
        with driver.session(database=client.connection.database) as session:
            result = session.run(query6)
            for record in result:
                print(f"  • {record['org.name']} ({record['org.category']})")
                if record['internal_units']:
                    units = [u for u in record['internal_units'] if u]
                    if units:
                        print(f"    Internal units: {', '.join(units[:3])}...")
        
    except Exception as e:
        print(f"❌ Error running queries: {e}")
        print("💡 Make sure you have processed some documents first:")
        print("   python main.py --documents documents")
    
    finally:
        client.close()


def show_graphrag_benefits():
    """Show how enhanced properties benefit GraphRAG."""
    print("\n\n🎯 GraphRAG Benefits of Enhanced Properties")
    print("=" * 50)
    
    benefits = [
        {
            "benefit": "Intelligent Ranking",
            "description": "graph_importance scores help prioritize entities in responses",
            "example": "Executives (1.0) ranked higher than office locations (0.6)"
        },
        {
            "benefit": "Semantic Understanding", 
            "description": "context_keywords enable better query matching",
            "example": "Search for 'leader' matches entities with 'manager' keywords"
        },
        {
            "benefit": "Structured Responses",
            "description": "Categories organize information logically",
            "example": "Group results by People, Organizations, Technology, etc."
        },
        {
            "benefit": "Relationship Discovery",
            "description": "Type flags guide graph traversal",
            "example": "leadership_role=true identifies management hierarchies"
        },
        {
            "benefit": "Contextual Filtering",
            "description": "Boolean properties enable targeted queries",
            "example": "is_technology=true finds only technical entities"
        }
    ]
    
    for i, benefit in enumerate(benefits, 1):
        print(f"\n{i}. {benefit['benefit']}")
        print(f"   {benefit['description']}")
        print(f"   Example: {benefit['example']}")


def main():
    """Run enhanced property query examples."""
    print("🚀 Enhanced Properties Query Examples")
    print("This script demonstrates GraphRAG-optimized queries using enhanced node properties.")
    print("\n⚠️  Note: Run 'python main.py --documents documents' first to populate the graph.")
    
    run_enhanced_queries()
    show_graphrag_benefits()
    
    print("\n\n✅ Query Examples Complete")
    print("💡 Use these patterns in your GraphRAG implementation for:")
    print("   • Better entity ranking and prioritization")
    print("   • Semantic search and matching")
    print("   • Structured knowledge organization")
    print("   • Intelligent relationship discovery")


if __name__ == "__main__":
    main()
