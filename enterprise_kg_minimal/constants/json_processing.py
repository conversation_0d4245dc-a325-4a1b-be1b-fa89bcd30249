"""
JSON Processing Constants for Enterprise KG

This module defines constants and configurations for processing JSON documents,
including specialized handling for different JSON data sources like Jira, APIs, etc.
"""

from typing import Dict, List, Any, Optional
from enum import Enum


class JsonSourceType(Enum):
    """Types of JSON data sources that can be processed."""
    GENERIC = "generic"
    JIRA_PROJECT = "jira_project"
    JIRA_ISSUE = "jira_issue"
    API_RESPONSE = "api_response"
    CONFIG_FILE = "config_file"
    DATABASE_EXPORT = "database_export"


# Field mapping configurations for different JSON source types
JSON_FIELD_MAPPINGS = {
    JsonSourceType.JIRA_PROJECT: {
        "primary_entity_fields": ["name", "key"],
        "person_fields": ["lead.displayName", "assignee.displayName", "reporter.displayName"],
        "organization_fields": ["projectCategory.name"],
        "component_fields": ["components[].name"],
        "relationship_indicators": {
            "lead": "LEADS",
            "assignee": "ASSIGNED_TO", 
            "reporter": "REPORTED_BY",
            "components": "CONTAINS"
        }
    },
    JsonSourceType.JIRA_ISSUE: {
        "primary_entity_fields": ["summary", "key"],
        "person_fields": ["assignee.displayName", "reporter.displayName"],
        "project_fields": ["project.name", "project.key"],
        "component_fields": ["components[].name"],
        "relationship_indicators": {
            "assignee": "ASSIGNED_TO",
            "reporter": "REPORTED_BY",
            "project": "BELONGS_TO"
        }
    },
    JsonSourceType.GENERIC: {
        "primary_entity_fields": ["name", "title", "id", "key"],
        "person_fields": ["owner", "creator", "author", "lead", "manager"],
        "organization_fields": ["company", "organization", "department", "team"],
        "relationship_indicators": {
            "owner": "OWNS",
            "creator": "CREATED_BY",
            "author": "AUTHORED_BY",
            "lead": "LEADS",
            "manager": "MANAGES"
        }
    }
}

# Common JSON field patterns that indicate entity types
ENTITY_TYPE_PATTERNS = {
    "Person": [
        "displayName", "fullName", "name", "username", "email",
        "firstName", "lastName", "author", "creator", "owner",
        "lead", "manager", "assignee", "reporter"
    ],
    "Project": [
        "projectName", "project", "projectKey", "initiative",
        "program", "campaign"
    ],
    "Company": [
        "company", "organization", "corporation", "firm",
        "vendor", "partner", "client"
    ],
    "Department": [
        "department", "division", "unit", "group", "team"
    ],
    "System": [
        "system", "application", "platform", "service",
        "tool", "software"
    ],
    "Component": [
        "component", "module", "feature", "capability"
    ]
}

# Relationship patterns for JSON processing
JSON_RELATIONSHIP_PATTERNS = {
    "hierarchical": {
        "parent": "PARENT_OF",
        "child": "CHILD_OF",
        "contains": "CONTAINS",
        "belongsTo": "BELONGS_TO",
        "partOf": "PART_OF"
    },
    "ownership": {
        "owner": "OWNS",
        "ownedBy": "OWNED_BY",
        "creator": "CREATED_BY",
        "createdBy": "CREATED_BY"
    },
    "assignment": {
        "assignee": "ASSIGNED_TO",
        "assignedTo": "ASSIGNED_TO",
        "responsible": "RESPONSIBLE_FOR",
        "lead": "LEADS",
        "manager": "MANAGES"
    },
    "collaboration": {
        "collaborator": "COLLABORATES_WITH",
        "partner": "PARTNERS_WITH",
        "contributor": "CONTRIBUTES_TO",
        "participant": "PARTICIPATES_IN"
    }
}

# JSON structure analysis patterns
JSON_ANALYSIS_PATTERNS = {
    "array_indicators": ["[]", "list", "items", "elements"],
    "nested_object_indicators": [".", "nested", "sub", "inner"],
    "relationship_indicators": ["_id", "Id", "ref", "reference", "link"],
    "metadata_fields": ["created", "updated", "modified", "timestamp", "date"]
}


def detect_json_source_type(json_data: Dict[str, Any]) -> JsonSourceType:
    """
    Detect the type of JSON source based on its structure and fields.
    
    Args:
        json_data: The parsed JSON data
        
    Returns:
        Detected JsonSourceType
    """
    # Check for Jira project indicators
    if all(field in json_data for field in ["key", "name", "projectTypeKey"]):
        return JsonSourceType.JIRA_PROJECT
    
    # Check for Jira issue indicators  
    if all(field in json_data for field in ["key", "summary", "issueType"]):
        return JsonSourceType.JIRA_ISSUE
    
    # Check for API response indicators
    if any(field in json_data for field in ["status", "data", "response", "result"]):
        return JsonSourceType.API_RESPONSE
    
    # Default to generic
    return JsonSourceType.GENERIC


def get_field_mappings(source_type: JsonSourceType) -> Dict[str, Any]:
    """
    Get field mappings for a specific JSON source type.
    
    Args:
        source_type: The type of JSON source
        
    Returns:
        Field mapping configuration
    """
    return JSON_FIELD_MAPPINGS.get(source_type, JSON_FIELD_MAPPINGS[JsonSourceType.GENERIC])


def extract_nested_value(data: Dict[str, Any], field_path: str) -> Optional[Any]:
    """
    Extract value from nested JSON using dot notation or array notation.
    
    Args:
        data: The JSON data
        field_path: Path to the field (e.g., "lead.displayName" or "components[].name")
        
    Returns:
        Extracted value or None if not found
    """
    try:
        # Handle array notation like "components[].name"
        if "[]" in field_path:
            array_path, item_field = field_path.split("[].")
            array_data = data.get(array_path, [])
            if isinstance(array_data, list):
                return [item.get(item_field) for item in array_data if isinstance(item, dict) and item_field in item]
        
        # Handle dot notation like "lead.displayName"
        elif "." in field_path:
            parts = field_path.split(".")
            current = data
            for part in parts:
                if isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return None
            return current
        
        # Simple field access
        else:
            return data.get(field_path)
            
    except (KeyError, TypeError, AttributeError):
        return None


def flatten_json_for_processing(json_data: Dict[str, Any], prefix: str = "") -> List[str]:
    """
    Flatten JSON data into a list of text statements for LLM processing.
    
    Args:
        json_data: The JSON data to flatten
        prefix: Prefix for nested fields
        
    Returns:
        List of text statements describing the JSON content
    """
    statements = []
    
    for key, value in json_data.items():
        current_key = f"{prefix}.{key}" if prefix else key
        
        if isinstance(value, dict):
            # Nested object
            statements.append(f"{current_key} contains nested information:")
            statements.extend(flatten_json_for_processing(value, current_key))
        
        elif isinstance(value, list):
            # Array
            if value:
                statements.append(f"{current_key} contains {len(value)} items:")
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        statements.extend(flatten_json_for_processing(item, f"{current_key}[{i}]"))
                    else:
                        statements.append(f"{current_key}[{i}] is {item}")
        
        else:
            # Simple value
            statements.append(f"{current_key} is {value}")
    
    return statements


def generate_json_context_description(json_data: Dict[str, Any], source_type: JsonSourceType) -> str:
    """
    Generate a context description for JSON data to help LLM understand the structure.
    
    Args:
        json_data: The JSON data
        source_type: The detected source type
        
    Returns:
        Context description string
    """
    descriptions = {
        JsonSourceType.JIRA_PROJECT: "This is a Jira project configuration containing project details, team members, and components.",
        JsonSourceType.JIRA_ISSUE: "This is a Jira issue containing task details, assignments, and project relationships.",
        JsonSourceType.API_RESPONSE: "This is an API response containing structured data from a web service.",
        JsonSourceType.GENERIC: "This is a JSON document containing structured data with entities and relationships."
    }
    
    base_description = descriptions.get(source_type, descriptions[JsonSourceType.GENERIC])
    
    # Add structure information
    structure_info = []
    if isinstance(json_data, dict):
        structure_info.append(f"Contains {len(json_data)} main fields")
        
        # Count nested objects and arrays
        nested_objects = sum(1 for v in json_data.values() if isinstance(v, dict))
        arrays = sum(1 for v in json_data.values() if isinstance(v, list))
        
        if nested_objects > 0:
            structure_info.append(f"{nested_objects} nested objects")
        if arrays > 0:
            structure_info.append(f"{arrays} arrays")
    
    if structure_info:
        base_description += f" {', '.join(structure_info)}."
    
    return base_description
