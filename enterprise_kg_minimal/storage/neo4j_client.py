"""
Neo4j graph database client for Enterprise KG

This module provides integration with Neo4j for storing and querying
knowledge graph data. Can work standalone or with CocoIndex.
"""

# import cocoindex  # Commented out for standalone usage
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from constants.schemas import GraphNode, GraphRelationship, EntityRelationship


@dataclass
class Neo4jConnection:
    """Connection specification for Neo4j."""

    uri: str
    user: str
    password: str
    database: Optional[str] = None


class Neo4jClient:
    """
    Client for interacting with Neo4j graph database.

    This class provides methods for storing and querying knowledge graph data
    following the CocoIndex Neo4j storage pattern.
    """

    def __init__(self, connection: Neo4jConnection):
        """
        Initialize the Neo4j client.

        Args:
            connection: Neo4j connection configuration
        """
        self.connection = connection
        self._driver = None

    def _get_driver(self):
        """Get or create Neo4j driver."""
        if self._driver is None:
            try:
                from neo4j import GraphDatabase
                self._driver = GraphDatabase.driver(
                    self.connection.uri,
                    auth=(self.connection.user, self.connection.password)
                )
            except ImportError:
                raise ImportError(
                    "Neo4j driver not installed. Install with: pip install neo4j"
                )
        return self._driver

    def close(self):
        """Close the Neo4j driver connection."""
        if self._driver:
            self._driver.close()
            self._driver = None

    def create_node(self, node: GraphNode) -> Dict[str, Any]:
        """
        Create a node in the Neo4j database.

        Args:
            node: Graph node to create

        Returns:
            Created node information
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            query = f"""
            MERGE (n:{node.label} {{name: $name}})
            SET n += $properties
            SET n.created_at = $created_at
            SET n.updated_at = $updated_at
            RETURN n
            """

            result = session.run(
                query,
                name=node.name,
                properties=node.properties,
                created_at=node.created_at or datetime.now(),
                updated_at=datetime.now()
            )

            return result.single()["n"]

    def create_relationship(self, relationship: GraphRelationship) -> Dict[str, Any]:
        """
        Create a relationship in the Neo4j database.

        Args:
            relationship: Graph relationship to create

        Returns:
            Created relationship information
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            query = f"""
            MATCH (source {{node_id: $source_node_id}})
            MATCH (target {{node_id: $target_node_id}})
            MERGE (source)-[r:{relationship.relationship_type}]->(target)
            SET r += $properties
            SET r.created_at = $created_at
            SET r.updated_at = $updated_at
            RETURN r
            """

            result = session.run(
                query,
                source_node_id=relationship.source_node_id,
                target_node_id=relationship.target_node_id,
                properties=relationship.properties,
                created_at=relationship.created_at or datetime.now(),
                updated_at=datetime.now()
            )

            return result.single()["r"]

    def create_entity_relationship(
        self,
        entity_rel: EntityRelationship,
        source_document: Optional[str] = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any], Dict[str, Any]]:
        """
        Create nodes and relationship from EntityRelationship.

        Args:
            entity_rel: Entity relationship to create
            source_document: Source document filename

        Returns:
            Tuple of (source_node, target_node, relationship)
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            # Determine node labels
            source_label = self._get_node_label(entity_rel.subject_type)
            target_label = self._get_node_label(entity_rel.object_type)

            # Get enhanced properties for both entities
            source_properties = self._get_enhanced_entity_properties(entity_rel.subject_type)
            target_properties = self._get_enhanced_entity_properties(entity_rel.object_type)

            # Create or merge source node with enhanced properties
            source_query = f"""
            MERGE (source:{source_label} {{name: $subject}})
            SET source.updated_at = $updated_at,
                source.created_at = COALESCE(source.created_at, $created_at),
                source.entity_type = $subject_type,
                source += $source_properties
            RETURN source
            """

            # Create or merge target node with enhanced properties
            target_query = f"""
            MERGE (target:{target_label} {{name: $object}})
            SET target.updated_at = $updated_at,
                target.created_at = COALESCE(target.created_at, $created_at),
                target.entity_type = $object_type,
                target += $target_properties
            RETURN target
            """

            # Create relationship
            rel_query = f"""
            MATCH (source:{source_label} {{name: $subject}})
            MATCH (target:{target_label} {{name: $object}})
            MERGE (source)-[r:{entity_rel.predicate.upper()}]->(target)
            SET r.confidence_score = $confidence_score,
                r.context = $context,
                r.source_sentence = $source_sentence,
                r.source_document = $source_document,
                r.updated_at = $updated_at,
                r.created_at = COALESCE(r.created_at, $created_at)
            RETURN r
            """

            now = datetime.now()

            # Execute queries
            source_result = session.run(
                source_query,
                subject=entity_rel.subject,
                subject_type=entity_rel.subject_type or "Entity",
                source_properties=source_properties,
                created_at=now,
                updated_at=now
            )

            target_result = session.run(
                target_query,
                object=entity_rel.object,
                object_type=entity_rel.object_type or "Entity",
                target_properties=target_properties,
                created_at=now,
                updated_at=now
            )

            rel_result = session.run(
                rel_query,
                subject=entity_rel.subject,
                object=entity_rel.object,
                confidence_score=entity_rel.confidence_score,
                context=entity_rel.context,
                source_sentence=entity_rel.source_sentence,
                source_document=source_document,
                created_at=now,
                updated_at=now
            )

            return (
                source_result.single()["source"],
                target_result.single()["target"],
                rel_result.single()["r"]
            )

    def batch_create_entity_relationships(
        self,
        entity_relationships: List[EntityRelationship],
        source_document: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Create multiple entity relationships in batch.

        Args:
            entity_relationships: List of entity relationships to create
            source_document: Source document filename

        Returns:
            List of creation results
        """
        results = []

        for entity_rel in entity_relationships:
            try:
                result = self.create_entity_relationship(entity_rel, source_document)
                results.append({
                    "success": True,
                    "entity_relationship": entity_rel,
                    "result": result
                })
            except Exception as e:
                results.append({
                    "success": False,
                    "entity_relationship": entity_rel,
                    "error": str(e)
                })

        return results

    def create_entity_relationship_with_org_context(
        self,
        entity_rel: EntityRelationship,
        source_document: Optional[str] = None,
        source_file_id: Optional[str] = None,
        org_context: Optional[Dict[str, str]] = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any], Dict[str, Any]]:
        """
        Create nodes and relationship with optional organizational context and file linking.

        This method extends the basic create_entity_relationship with:
        - Organizational context properties
        - File node linking via CONTAINS relationships
        - Context hierarchy for context-aware search

        Args:
            entity_rel: Entity relationship to create
            source_document: Source document filename
            source_file_id: Optional ID of existing file node to link to
            org_context: Optional organizational context (org_id, dept_id, team_id)

        Returns:
            Tuple of (source_node, target_node, relationship)
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            # Determine node labels
            source_label = self._get_node_label(entity_rel.subject_type)
            target_label = self._get_node_label(entity_rel.object_type)

            # Get enhanced properties for both entities
            source_properties = self._get_enhanced_entity_properties(entity_rel.subject_type)
            target_properties = self._get_enhanced_entity_properties(entity_rel.object_type)

            # Add organizational context to properties if provided
            if org_context:
                source_properties.update({
                    "org_id": org_context.get("org_id"),
                    "department_id": org_context.get("dept_id"),
                    "team_id": org_context.get("team_id"),
                    "context_type": "organizational",
                    "context_hierarchy": [
                        org_context.get("org_id"),
                        org_context.get("dept_id"),
                        org_context.get("team_id")
                    ]
                })
                target_properties.update({
                    "org_id": org_context.get("org_id"),
                    "department_id": org_context.get("dept_id"),
                    "team_id": org_context.get("team_id"),
                    "context_type": "organizational",
                    "context_hierarchy": [
                        org_context.get("org_id"),
                        org_context.get("dept_id"),
                        org_context.get("team_id")
                    ]
                })

            # Create or merge source node with enhanced properties
            source_query = f"""
            MERGE (source:{source_label} {{name: $subject}})
            SET source.updated_at = $updated_at,
                source.created_at = COALESCE(source.created_at, $created_at),
                source.entity_type = $subject_type,
                source += $source_properties
            RETURN source
            """

            # Create or merge target node with enhanced properties
            target_query = f"""
            MERGE (target:{target_label} {{name: $object}})
            SET target.updated_at = $updated_at,
                target.created_at = COALESCE(target.created_at, $created_at),
                target.entity_type = $object_type,
                target += $target_properties
            RETURN target
            """

            # Create relationship
            rel_query = f"""
            MATCH (source:{source_label} {{name: $subject}})
            MATCH (target:{target_label} {{name: $object}})
            MERGE (source)-[r:{entity_rel.predicate.upper()}]->(target)
            SET r.confidence_score = $confidence_score,
                r.context = $context,
                r.source_sentence = $source_sentence,
                r.source_document = $source_document,
                r.source_file_id = $source_file_id,
                r.updated_at = $updated_at,
                r.created_at = COALESCE(r.created_at, $created_at)
            RETURN r
            """

            now = datetime.now()

            # Execute queries
            source_result = session.run(
                source_query,
                subject=entity_rel.subject,
                subject_type=entity_rel.subject_type or "Entity",
                source_properties=source_properties,
                created_at=now,
                updated_at=now
            )

            target_result = session.run(
                target_query,
                object=entity_rel.object,
                object_type=entity_rel.object_type or "Entity",
                target_properties=target_properties,
                created_at=now,
                updated_at=now
            )

            rel_result = session.run(
                rel_query,
                subject=entity_rel.subject,
                object=entity_rel.object,
                confidence_score=entity_rel.confidence_score,
                context=entity_rel.context,
                source_sentence=entity_rel.source_sentence,
                source_document=source_document,
                source_file_id=source_file_id,
                created_at=now,
                updated_at=now
            )

            # Create file-to-entity relationships if source_file_id is provided
            if source_file_id:
                self._create_file_entity_links(session, source_file_id, entity_rel, now)

            return (
                source_result.single()["source"],
                target_result.single()["target"],
                rel_result.single()["r"]
            )

    def _create_file_entity_links(
        self,
        session,
        source_file_id: str,
        entity_rel: EntityRelationship,
        timestamp: datetime
    ):
        """Create CONTAINS relationships from file to extracted entities."""

        file_link_query = """
        MATCH (file {node_id: $file_id})
        MATCH (subject {name: $subject_name})
        MATCH (object {name: $object_name})

        MERGE (file)-[:FILE_CONTAINS {
            extraction_date: $extraction_date,
            confidence: $confidence,
            relationship_type: $relationship_type
        }]->(subject)

        MERGE (file)-[:FILE_CONTAINS {
            extraction_date: $extraction_date,
            confidence: $confidence,
            relationship_type: $relationship_type
        }]->(object)
        """

        session.run(
            file_link_query,
            file_id=source_file_id,
            subject_name=entity_rel.subject,
            object_name=entity_rel.object,
            extraction_date=timestamp,
            confidence=entity_rel.confidence_score or 0.5,
            relationship_type=entity_rel.predicate
        )

    def query_entities(
        self,
        entity_name: Optional[str] = None,
        entity_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Query entities from the graph.

        Args:
            entity_name: Optional entity name filter
            entity_type: Optional entity type filter
            limit: Maximum number of results

        Returns:
            List of matching entities
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            conditions = []
            params = {"limit": limit}

            if entity_name:
                conditions.append("n.name CONTAINS $entity_name")
                params["entity_name"] = entity_name

            if entity_type:
                conditions.append(f"n:{entity_type}")

            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""

            query = f"""
            MATCH (n)
            {where_clause}
            RETURN n
            LIMIT $limit
            """

            result = session.run(query, **params)
            return [record["n"] for record in result]

    def query_relationships(
        self,
        source_entity: Optional[str] = None,
        target_entity: Optional[str] = None,
        relationship_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Query relationships from the graph.

        Args:
            source_entity: Optional source entity name filter
            target_entity: Optional target entity name filter
            relationship_type: Optional relationship type filter
            limit: Maximum number of results

        Returns:
            List of matching relationships with source and target nodes
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            conditions = []
            params = {"limit": limit}

            if source_entity:
                conditions.append("source.name = $source_entity")
                params["source_entity"] = source_entity

            if target_entity:
                conditions.append("target.name = $target_entity")
                params["target_entity"] = target_entity

            rel_pattern = f"[r:{relationship_type}]" if relationship_type else "[r]"
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""

            query = f"""
            MATCH (source)-{rel_pattern}->(target)
            {where_clause}
            RETURN source, r, target
            LIMIT $limit
            """

            result = session.run(query, **params)
            return [
                {
                    "source": record["source"],
                    "relationship": record["r"],
                    "target": record["target"]
                }
                for record in result
            ]

    def get_entity_neighbors(
        self,
        entity_name: str,
        relationship_types: Optional[List[str]] = None,
        direction: str = "both",  # "in", "out", "both"
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get neighboring entities for a given entity.

        Args:
            entity_name: Name of the central entity
            relationship_types: Optional list of relationship types to filter
            direction: Direction of relationships ("in", "out", "both")
            limit: Maximum number of results

        Returns:
            List of neighboring entities with relationship information
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            rel_types_filter = ""
            if relationship_types:
                rel_types = "|".join(relationship_types)
                rel_types_filter = f":{rel_types}"

            if direction == "out":
                pattern = f"(entity)-[r{rel_types_filter}]->(neighbor)"
            elif direction == "in":
                pattern = f"(entity)<-[r{rel_types_filter}]-(neighbor)"
            else:  # both
                pattern = f"(entity)-[r{rel_types_filter}]-(neighbor)"

            query = f"""
            MATCH {pattern}
            WHERE entity.name = $entity_name
            RETURN neighbor, r, type(r) as relationship_type
            LIMIT $limit
            """

            result = session.run(query, entity_name=entity_name, limit=limit)
            return [
                {
                    "neighbor": record["neighbor"],
                    "relationship": record["r"],
                    "relationship_type": record["relationship_type"]
                }
                for record in result
            ]

    def _get_node_label(self, entity_type: Optional[str]) -> str:
        """
        Get the appropriate Neo4j node label for an entity type.

        Args:
            entity_type: The entity type string

        Returns:
            Valid Neo4j node label
        """
        if not entity_type:
            return "Entity"

        # Clean the entity type to make it a valid Neo4j label
        # Remove spaces, special characters, and ensure it starts with a letter
        import re
        cleaned = re.sub(r'[^a-zA-Z0-9_]', '', entity_type.replace(' ', ''))

        # Ensure it starts with a letter
        if cleaned and not cleaned[0].isalpha():
            cleaned = f"Entity{cleaned}"

        return cleaned if cleaned else "Entity"

    def _get_enhanced_entity_properties(self, entity_type: Optional[str]) -> Dict[str, Any]:
        """
        Get enhanced properties for an entity type from constants.

        Args:
            entity_type: The entity type string

        Returns:
            Dictionary of enhanced properties for GraphRAG context
        """
        if not entity_type:
            entity_type = "Entity"

        try:
            # Import here to avoid circular imports
            from constants.entities import get_entity_properties
            return get_entity_properties(entity_type)
        except ImportError:
            # Fallback if constants not available
            return {
                "description": f"A {entity_type.lower()} entity",
                "category": "General",
                "searchable": True,
                "graph_importance": 0.5
            }


# CocoIndex integration functions (commented out for standalone usage)
"""
def create_neo4j_storage_spec(
    connection: Neo4jConnection,
    node_label: str = "Entity",
    relationship_mapping: Optional[Dict[str, str]] = None
):
    # This function requires CocoIndex - commented out for standalone usage
    # Uncomment when integrating with CocoIndex
    pass
"""


def create_default_neo4j_client(
    uri: str = "bolt://localhost:7687",
    user: str = "neo4j",
    password: str = "cocoindex",
    database: Optional[str] = None
) -> Neo4jClient:
    """
    Create a default Neo4j client with standard settings.

    Args:
        uri: Neo4j URI
        user: Neo4j username
        password: Neo4j password
        database: Optional database name

    Returns:
        Configured Neo4jClient instance
    """
    connection = Neo4jConnection(
        uri=uri,
        user=user,
        password=password,
        database=database
    )

    return Neo4jClient(connection)
