# Standalone Enterprise KG Requirements
# Install with: pip install -r requirements_standalone.txt

# Core dependencies
neo4j>=5.0.0
python-dotenv>=1.0.0

# Document processing
pypdf>=3.0.0  # For PDF support
python-docx>=0.8.11  # For DOCX support

# LLM providers (choose one or more)
openai>=1.0.0
anthropic>=0.7.0

# Optional: For Google Gemini
# google-generativeai>=0.3.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0

# Code formatting
black>=23.0.0
isort>=5.0.0
