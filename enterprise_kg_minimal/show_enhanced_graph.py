#!/usr/bin/env python3
"""
Display the enhanced graph structure with all the new properties.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from storage.neo4j_client import Neo4jClient, Neo4jConnection


def show_enhanced_graph():
    """Display the enhanced graph with all properties."""
    print("🎯 Enhanced Knowledge Graph with GraphRAG Properties")
    print("=" * 60)
    
    connection = Neo4jConnection(
        uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        user=os.getenv("NEO4J_USER", "neo4j"),
        password=os.getenv("NEO4J_PASSWORD", "password"),
        database=os.getenv("NEO4J_DATABASE")
    )
    client = Neo4jClient(connection)
    
    try:
        driver = client._get_driver()
        with driver.session(database=client.connection.database) as session:
            
            # 1. Show all nodes with enhanced properties
            print("\n📋 All Nodes with Enhanced Properties:")
            print("-" * 50)
            result = session.run("""
                MATCH (n)
                RETURN n.name, n.entity_type, n.category, n.graph_importance, 
                       n.description, n.is_human, n.leadership_role, n.is_organization,
                       n.is_initiative, n.can_employ_people
                ORDER BY n.graph_importance DESC
            """)
            
            for record in result:
                name = record['n.name']
                entity_type = record['n.entity_type']
                category = record['n.category']
                importance = record['n.graph_importance']
                description = record['n.description']
                
                print(f"\n🔹 {name} ({entity_type})")
                print(f"   Category: {category}")
                print(f"   Importance: {importance:.2f}")
                print(f"   Description: {description}")
                
                # Show type-specific flags
                flags = []
                if record['n.is_human']:
                    flags.append("👤 Human")
                if record['n.leadership_role']:
                    flags.append("👑 Leader")
                if record['n.is_organization']:
                    flags.append("🏢 Organization")
                if record['n.is_initiative']:
                    flags.append("📋 Initiative")
                if record['n.can_employ_people']:
                    flags.append("💼 Employer")
                
                if flags:
                    print(f"   Flags: {', '.join(flags)}")
            
            # 2. Show relationships with context
            print("\n\n🔗 Relationships with Context:")
            print("-" * 50)
            result = session.run("""
                MATCH (source)-[r]->(target)
                RETURN source.name, type(r) as rel_type, target.name,
                       r.confidence_score, r.context, r.source_document
                ORDER BY r.confidence_score DESC
            """)
            
            for record in result:
                source = record['source.name']
                rel_type = record['rel_type']
                target = record['target.name']
                confidence = record['r.confidence_score']
                context = record['r.context']
                
                print(f"• {source} --[{rel_type}]--> {target}")
                print(f"  Confidence: {confidence:.1f}, Context: {context}")
            
            # 3. Show high-importance entities
            print("\n\n⭐ High-Importance Entities (GraphRAG Priority):")
            print("-" * 50)
            result = session.run("""
                MATCH (n)
                WHERE n.graph_importance > 0.8
                RETURN n.name, n.entity_type, n.graph_importance, n.category
                ORDER BY n.graph_importance DESC
            """)
            
            for record in result:
                name = record['n.name']
                entity_type = record['n.entity_type']
                importance = record['n.graph_importance']
                category = record['n.category']
                
                print(f"🌟 {name} ({entity_type}) - {importance:.2f} - {category}")
            
            # 4. Show entities by category
            print("\n\n📊 Entities by Category:")
            print("-" * 50)
            result = session.run("""
                MATCH (n)
                WHERE n.category IS NOT NULL
                RETURN n.category, collect(n.name) as entities, count(n) as count
                ORDER BY count DESC
            """)
            
            for record in result:
                category = record['n.category']
                count = record['count']
                entities = record['entities']
                
                print(f"📁 {category} ({count} entities):")
                for entity in entities[:5]:  # Show first 5
                    print(f"   • {entity}")
                if len(entities) > 5:
                    print(f"   ... and {len(entities) - 5} more")
            
            # 5. Show context keywords for semantic search
            print("\n\n🔍 Context Keywords for Semantic Search:")
            print("-" * 50)
            result = session.run("""
                MATCH (n)
                WHERE n.context_keywords IS NOT NULL
                RETURN n.name, n.entity_type, n.context_keywords
                LIMIT 5
            """)
            
            for record in result:
                name = record['n.name']
                entity_type = record['n.entity_type']
                keywords = record['n.context_keywords']
                
                print(f"🏷️  {name} ({entity_type})")
                print(f"   Keywords: {', '.join(keywords[:5])}...")
            
            # 6. Show typical relationships
            print("\n\n🔗 Typical Relationships (GraphRAG Hints):")
            print("-" * 50)
            result = session.run("""
                MATCH (n)
                WHERE n.typical_relationships IS NOT NULL
                RETURN n.name, n.entity_type, n.typical_relationships
                LIMIT 5
            """)
            
            for record in result:
                name = record['n.name']
                entity_type = record['n.entity_type']
                typical_rels = record['n.typical_relationships']
                
                print(f"🔄 {name} ({entity_type})")
                print(f"   Expected relationships: {', '.join(typical_rels[:3])}...")
            
    except Exception as e:
        print(f"❌ Error querying graph: {e}")
    finally:
        client.close()


def show_sample_graphrag_queries():
    """Show sample GraphRAG-optimized queries."""
    print("\n\n🎯 Sample GraphRAG-Optimized Queries:")
    print("=" * 60)
    
    queries = [
        {
            "name": "Find Key Decision Makers",
            "query": """
            MATCH (leader)
            WHERE leader.leadership_role = true 
              AND leader.graph_importance > 0.9
            RETURN leader.name, leader.entity_type, leader.graph_importance
            ORDER BY leader.graph_importance DESC
            """,
            "benefit": "Prioritizes high-importance leaders for executive queries"
        },
        {
            "name": "Semantic Search by Keywords",
            "query": """
            MATCH (n)
            WHERE any(keyword IN n.context_keywords 
                     WHERE keyword CONTAINS 'manager')
            RETURN n.name, n.entity_type, n.context_keywords
            """,
            "benefit": "Enables semantic matching beyond exact entity names"
        },
        {
            "name": "Organize Knowledge by Category",
            "query": """
            MATCH (n)
            WHERE n.category = 'Organizations'
              AND n.graph_importance > 0.8
            RETURN n.name, n.description, n.graph_importance
            ORDER BY n.graph_importance DESC
            """,
            "benefit": "Structured responses organized by entity categories"
        },
        {
            "name": "Find Integration Opportunities",
            "query": """
            MATCH (org)
            WHERE org.can_employ_people = true
            MATCH (org)-[r]-(project)
            WHERE project.is_initiative = true
            RETURN org.name, type(r), project.name
            """,
            "benefit": "Uses type flags to discover business relationships"
        }
    ]
    
    for i, query_info in enumerate(queries, 1):
        print(f"\n{i}. {query_info['name']}")
        print(f"   Benefit: {query_info['benefit']}")
        print(f"   Query:")
        for line in query_info['query'].strip().split('\n'):
            print(f"     {line.strip()}")


def main():
    """Main function."""
    show_enhanced_graph()
    show_sample_graphrag_queries()
    
    print("\n\n✅ Enhanced Graph Analysis Complete!")
    print("🎯 Key GraphRAG Benefits Demonstrated:")
    print("   • Rich semantic properties on every node")
    print("   • Importance scoring for result prioritization")
    print("   • Category-based knowledge organization")
    print("   • Context keywords for semantic search")
    print("   • Type-specific flags for targeted filtering")
    print("   • Relationship hints for graph traversal")
    
    print(f"\n💡 Access your enhanced graph at:")
    print(f"   Neo4j Browser: {os.getenv('NEO4J_URI', 'bolt://localhost:7687')}")
    print("   Try the sample queries above to see GraphRAG optimization in action!")


if __name__ == "__main__":
    main()
