"""
Enterprise Knowledge Graph - Minimal Standalone Package

A minimal standalone enterprise knowledge graph system that extracts entities 
and relationships from documents and stores them in Neo4j.

This package contains only the essential files needed for standalone operation.
"""

from .constants.entities import EntityType
from .constants.relationships import RelationshipType
from .constants.schemas import EntityRelationship, DocumentSummary, ProcessingMetadata

__version__ = "0.1.0-minimal"
__author__ = "Enterprise KG Team"

__all__ = [
    "EntityType",
    "RelationshipType",
    "EntityRelationship",
    "DocumentSummary",
    "ProcessingMetadata",
]
